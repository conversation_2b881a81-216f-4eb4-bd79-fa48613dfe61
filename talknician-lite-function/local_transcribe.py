import sys
from app.utils import transcribe_with_azure_stt


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python local_transcribe.py <path_to_audio.wav>")
        sys.exit(1)
    audio_path = sys.argv[1]
    print(f"Transcribing {audio_path}...")
    srt_content = transcribe_with_azure_stt(audio_path)
    with open("output.srt", "w") as f:
        f.write(srt_content)
    print("SRT file generated: output.srt") 