import logging
from sqlalchemy.orm import Session
from app.models import <PERSON><PERSON><PERSON>, Job<PERSON>tatus, SessionLocal
from app.utils import (
    download_video, 
    extract_audio_with_moviepy, 
    transcribe_with_azure_stt, 
    upload_to_blob,
    cleanup_files
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_job_status(session: Session, job: VideoJob, status: JobStatus, error: str = None):
    """Update job status in database"""
    job.status = status
    if error:
        job.error_message = error
    session.commit()
    logger.info(f"Job {job.id} status updated to {status.value} (error: {error})")

def process_video_job(job_id: int):
    """Main function to process video-to-SRT job"""
    session = SessionLocal()
    video_path = None
    audio_path = None
    
    try:
        # Get job from database
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if not job:
            logger.error(f"Job {job_id} not found")
            return
        
        logger.info(f"Starting job {job_id}: {job.video_url}")
        
        # Step 1: Download video
        update_job_status(session, job, JobStatus.DOWNLOADING)
        logger.info(f"Downloading video for job {job_id} from {job.video_url}")
        video_path = download_video(job.video_url)
        logger.info(f"Video downloaded to: {video_path}")
        
        # Step 2: Convert video to audio
        update_job_status(session, job, JobStatus.CONVERTING_TO_AUDIO)
        logger.info(f"Extracting audio from video for job {job_id}")
        audio_path = extract_audio_with_moviepy(video_path)
        logger.info(f"Audio extracted to: {audio_path}")
        
        # Step 3: Transcribe audio to text
        update_job_status(session, job, JobStatus.TRANSCRIBING)
        logger.info(f"Transcribing audio for job {job_id}")
        srt_text = transcribe_with_azure_stt(audio_path)
        logger.info(f"Transcription completed for job {job_id}, SRT length: {len(srt_text)} characters")
        logger.info(f"SRT content for job {job_id}:\n{srt_text}")
        
        # Step 4: Upload SRT and audio to blob storage
        update_job_status(session, job, JobStatus.UPLOADING)
        logger.info(f"Uploading SRT and audio for job {job_id} to blob storage")
        urls = upload_to_blob(job_id, srt_text)
        job.srt_url = urls["srt_url"]
        logger.info(f"SRT uploaded to: {urls['srt_url']}")
        
        # Step 5: Mark as completed
        update_job_status(session, job, JobStatus.COMPLETED)
        logger.info(f"Job {job_id} completed successfully")
        
    except Exception as e:
        import traceback
        logger.error(f"Job {job_id} failed: {str(e)}\n{traceback.format_exc()}")
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if job:
            update_job_status(session, job, JobStatus.FAILED, str(e))
    
    finally:
        # Clean up temporary files
        logger.info(f"Cleaning up files for job {job_id}: video_path={video_path}, audio_path={audio_path}")
        cleanup_files(video_path, audio_path)
        session.close()
        logger.info(f"Cleanup completed for job {job_id}")

def get_job_status(job_id: int) -> dict:
    """Get job status and details"""
    session = SessionLocal()
    try:
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if not job:
            return {"error": "Job not found"}
        
        return {
            "job_id": job.id,
            "video_url": job.video_url,
            "status": job.status.value,
            "created_at": job.created_at.isoformat(),
            "srt_url": job.srt_url,
            "error_message": job.error_message
        }
    finally:
        session.close()