import requests
import os
import tempfile
from moviepy.editor import AudioFileClip
import azure.cognitiveservices.speech as speechsdk
from azure.storage.blob import BlobServiceClient
from datetime import datetime, timedelta
import time
import logging
import wave
import threading

logger = logging.getLogger(__name__)

def download_video(video_url: str) -> str:
    """Download video from URL and return local path"""
    response = requests.get(video_url, stream=True)
    response.raise_for_status()
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_file:
        for chunk in response.iter_content(chunk_size=8192):
            temp_file.write(chunk)
        return temp_file.name

def extract_audio_with_moviepy(video_path: str) -> str:
    """Extract audio from video using AudioFileClip (ensures compatibility with Azure STT)"""
    try:
        base, _ = os.path.splitext(video_path)
        audio_path = base + ".wav"
        audio_clip = AudioFileClip(video_path)
        # Use specific audio settings for Azure Speech SDK compatibility
        audio_clip.write_audiofile(
            audio_path,
            codec='pcm_s16le',
            ffmpeg_params=['-ac', '1', '-ar', '16000'],  # Mono, 16kHz
            verbose=False,
            logger=None
        )
        audio_clip.close()
        return audio_path
    except Exception as e:
        raise Exception(f"Audio extraction failed: {str(e)}")

def transcribe_with_azure_stt(audio_path: str) -> str:
    """Transcribe audio using Azure Speech-to-Text with improved error handling"""
    speech_key = os.getenv("AZURE_SPEECH_KEY")
    speech_region = os.getenv("AZURE_SPEECH_REGION")
    
    if not speech_key or not speech_region:
        raise Exception("Azure Speech credentials not configured")
    
    try:
        # Log audio file information
        logger.info(f"Audio file size: {os.path.getsize(audio_path)} bytes")
        with wave.open(audio_path, 'rb') as wf:
            duration = wf.getnframes() / wf.getframerate()
            logger.info(f"Audio duration: {duration} seconds, channels: {wf.getnchannels()}, framerate: {wf.getframerate()}, sample width: {wf.getsampwidth()}")
        
        # Configure speech service with explicit settings
        speech_config = speechsdk.SpeechConfig(
            subscription=speech_key, 
            region=speech_region
        )
        speech_config.speech_recognition_language = "en-US"
        speech_config.set_profanity(speechsdk.ProfanityOption.Masked)
        
        # Try different transcription methods in order of preference
        methods = [
            ("continuous_recognition", continuous_recognition_transcribe),
            # ("single_shot_extended", single_shot_extended_transcribe),
            # ("chunked_transcription", chunked_transcribe)
        ]
        
        for method_name, method_func in methods:
            try:
                logger.info(f"Attempting transcription with method: {method_name}")
                result = method_func(audio_path, speech_config)
                if result:
                    logger.info(f"Successfully transcribed using {method_name}")
                    return result
                else:
                    logger.warning(f"Method {method_name} returned empty result")
            except Exception as e:
                logger.error(f"Method {method_name} failed: {str(e)}")
                continue
        
        logger.error("All transcription methods failed")
        return ""
        
    except Exception as e:
        logger.error(f"Transcription setup failed: {str(e)}")
        return ""

def continuous_recognition_transcribe(audio_path: str, speech_config: speechsdk.SpeechConfig) -> str:
    """Continuous recognition transcription method"""
    audio_config = speechsdk.AudioConfig(filename=audio_path)
    recognizer = speechsdk.SpeechRecognizer(
        speech_config=speech_config, 
        audio_config=audio_config
    )
    
    all_results = []
    done = threading.Event()
    
    def handle_result(evt):
        if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
            all_results.append({
                'text': evt.result.text,
                'offset': evt.result.offset,
                'duration': evt.result.duration
            })
            logger.info(f"Recognized: {evt.result.text}")
    
    def handle_session_stopped(evt):
        logger.info("Session stopped")
        done.set()
    
    def handle_canceled(evt):
        logger.error(f"Recognition canceled: {evt.result.cancellation_details}")
        done.set()
    
    # Connect event handlers
    recognizer.recognized.connect(handle_result)
    recognizer.session_stopped.connect(handle_session_stopped)
    recognizer.canceled.connect(handle_canceled)
    
    # Start recognition
    recognizer.start_continuous_recognition()
    
    # Wait for completion with timeout
    if done.wait(timeout=1200):  # 20 minutes timeout
        recognizer.stop_continuous_recognition()
        return convert_to_srt(all_results)
    else:
        recognizer.stop_continuous_recognition()
        raise Exception("Continuous recognition timeout")

def single_shot_extended_transcribe(audio_path: str, speech_config: speechsdk.SpeechConfig) -> str:
    """Single shot transcription for shorter audio files"""
    audio_config = speechsdk.AudioConfig(filename=audio_path)
    recognizer = speechsdk.SpeechRecognizer(
        speech_config=speech_config, 
        audio_config=audio_config
    )
    
    # For longer audio, we need to use recognize_once_async with timeout
    try:
        result = recognizer.recognize_once()
        
        if result.reason == speechsdk.ResultReason.RecognizedSpeech:
            # Create a single result entry
            results = [{
                'text': result.text,
                'offset': 0,
                'duration': 10000000 * 30  # Assume 30 seconds if we can't get duration
            }]
            return convert_to_srt(results)
        elif result.reason == speechsdk.ResultReason.NoMatch:
            logger.warning("No speech could be recognized")
            return ""
        elif result.reason == speechsdk.ResultReason.Canceled:
            logger.error(f"Recognition canceled: {result.cancellation_details}")
            raise Exception(f"Recognition canceled: {result.cancellation_details}")
        
    except Exception as e:
        raise Exception(f"Single shot transcription failed: {str(e)}")

def chunked_transcribe(audio_path: str, speech_config: speechsdk.SpeechConfig) -> str:
    """Chunked transcription for very long audio files"""
    try:
        # Get audio duration
        with wave.open(audio_path, 'rb') as wf:
            duration = wf.getnframes() / wf.getframerate()
        
        # If audio is shorter than 30 seconds, use single shot
        if duration < 30:
            return single_shot_extended_transcribe(audio_path, speech_config)
        
        # For longer audio, split into chunks
        chunk_duration = 30  # 30 seconds per chunk
        chunks = int(duration / chunk_duration) + 1
        
        all_results = []
        
        for i in range(chunks):
            start_time = i * chunk_duration
            end_time = min((i + 1) * chunk_duration, duration)
            
            # Create chunk (this is a simplified approach - in practice, you'd need to actually split the audio)
            # For now, we'll just try the whole file
            if i == 0:  # Only process first chunk for demo
                result = single_shot_extended_transcribe(audio_path, speech_config)
                return result
        
        return convert_to_srt(all_results)
        
    except Exception as e:
        raise Exception(f"Chunked transcription failed: {str(e)}")

def convert_to_srt(results: list) -> str:
    """Convert transcription results to SRT format"""
    if not results:
        logger.warning("No transcription results to convert to SRT")
        return ""
    
    srt_lines = []
    
    for i, result in enumerate(results, 1):
        # Convert ticks to seconds (Azure returns time in 100-nanosecond intervals)
        start_seconds = result['offset'] / 10000000
        end_seconds = (result['offset'] + result['duration']) / 10000000
        
        # Format timestamps
        start_time = format_timestamp(start_seconds)
        end_time = format_timestamp(end_seconds)
        
        # Add SRT entry
        srt_lines.append(f"{i}")
        srt_lines.append(f"{start_time} --> {end_time}")
        srt_lines.append(result['text'])
        srt_lines.append("")  # Empty line between entries
    
    return "\n".join(srt_lines)

def format_timestamp(seconds: float) -> str:
    """Format seconds to SRT timestamp format (HH:MM:SS,mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def upload_to_blob(job_id: int, srt_text: str) -> dict:
    """Upload SRT file to Azure Blob Storage. Returns dict with SRT URL only."""
    connection_string = os.getenv("AZURE_BLOB_CONNECTION")
    container_name = os.getenv("AZURE_BLOB_CONTAINER", "subtitles")
    
    if not connection_string:
        raise Exception("Azure Blob Storage connection string not configured")
    
    try:
        blob_service = BlobServiceClient.from_connection_string(connection_string)
        container_client = blob_service.get_container_client(container_name)
        
        # Create container if it doesn't exist
        try:
            container_client.create_container()
        except:
            pass  # Container already exists
        
        # Use a folder per job
        job_folder = f"job-{job_id}"
        srt_blob_name = f"{job_folder}/job-{job_id}.srt"
        blob_client = container_client.get_blob_client(srt_blob_name)
        blob_client.upload_blob(srt_text, overwrite=True)
        
        # Return public URL for SRT
        account_name = blob_service.account_name
        srt_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{srt_blob_name}"
        
        return {"srt_url": srt_url}
        
    except Exception as e:
        raise Exception(f"Upload failed: {str(e)}")

def cleanup_files(*file_paths):
    """Clean up temporary files"""
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # Ignore cleanup errors