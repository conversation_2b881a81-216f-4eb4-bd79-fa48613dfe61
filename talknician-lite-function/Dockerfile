FROM python:3.11-slim

# Install system dependencies for Azure Speech SDK
RUN apt-get update && apt-get install -y \
    ffmpeg \
    wget \
    curl \
    ca-certificates \
    libasound2-dev \
    libssl-dev \
    libc6-dev \
    libstdc++6 \
    libgcc-s1 \
    libcurl4-openssl-dev \
    pkg-config \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directory for temporary files
RUN mkdir -p /tmp/video-processing

# Set environment variables for Azure Speech SDK
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV AZURE_SPEECH_RESOURCE_REGION=centralus

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]