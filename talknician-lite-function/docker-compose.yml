version: "3.8"

services:
  video-srt-api:
    build: .
    ports:
      - "8000:80"
    volumes:
      - .:/app
      - /tmp/video-processing:/tmp/video-processing
    environment:
      - DATABASE_URL=sqlite:///jobs.db
      - AZURE_SPEECH_KEY=${A<PERSON><PERSON><PERSON>_SPEECH_KEY}
      - A<PERSON><PERSON><PERSON>_SPEECH_REGION=${AZURE_SPEECH_REGION}
      - AZURE_BLOB_CONNECTION=${AZURE_BLOB_CONNECTION}
      - AZURE_BLOB_CONTAINER=${AZURE_BLOB_CONTAINER:-subtitles}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
