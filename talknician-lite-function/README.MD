# Video-to-SRT API

A scalable, stateless REST API that converts video files to SRT subtitle files using Azure Speech-to-Text, built with FastAPI and deployable to Azure Container Apps.

## Features

- **Detailed Status Tracking**: Monitor job progress through multiple stages
- **Azure Speech-to-Text Integration**: High-quality transcription service
- **MoviePy Audio Extraction**: Reliable video-to-audio conversion
- **Azure Blob Storage**: Secure file storage and retrieval
- **Stateless Architecture**: Horizontally scalable design
- **RESTful API**: Easy integration with existing systems

## Job Status Flow

1. **PENDING** - Job submitted, waiting to start
2. **DOWNLOADING** - Downloading video from provided URL
3. **CONVERTING_TO_AUDIO** - Using MoviePy to extract audio from video
4. **TRANSCRIBING** - Using Azure Speech-to-Text to convert audio to text
5. **UPLOADING** - Uploading generated SRT file to Azure Blob Storage
6. **COMPLETED** - Job finished successfully, SRT file ready for download
7. **FAILED** - An error occurred during processing

## Quick Start

### Prerequisites

- Python 3.11+
- Docker (for containerization)
- Azure Speech Services subscription
- Azure Blob Storage account

### Environment Setup

1. Clone the repository and navigate to the project directory
2. Copy `.env.example` to `.env` and fill in your Azure credentials:

```bash
cp .env.example .env
```

3. Update the `.env` file with your Azure credentials:

```env
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here
AZURE_BLOB_CONNECTION=your_blob_connection_string_here
AZURE_BLOB_CONTAINER=subtitles
```

### Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

3. Access the API:
   - API: http://localhost:8000
   - Interactive docs: http://localhost:8000/docs
   - Health check: http://localhost:8000/health

### Docker Development

1. Build and run with Docker Compose:
```bash
docker-compose up --build
```

2. The API will be available at http://localhost:8000

## API Endpoints

### Submit Job
```http
POST /jobs/
Content-Type: application/json

{
  "video_url": "https://example.com/video.mp4"
}
```

Response:
```json
{
  "job_id": 1,
  "status": "PENDING",
  "message": "Job submitted successfully"
}
```

### Check Job Status
```http
GET /jobs/{job_id}
```

Response:
```json
{
  "job_id": 1,
  "video_url": "https://example.com/video.mp4",
  "status": "COMPLETED",
  "created_at": "2025-07-14T10:30:00",
  "srt_url": "https://yourstorage.blob.core.windows.net/subtitles/job-1.srt",
  "error_message": null
}
```

### Download SRT File
```http
GET /jobs/{job_id}/download
```

Response:
```json
{
  "download_url": "https://yourstorage.blob.core.windows.net/subtitles/job-1.srt"
}
```

### List All Jobs
```http
GET /jobs/?skip=0&limit=10
```

## Azure Deployment

### Automated Deployment

1. Make the deployment script executable:
```bash
chmod +x deploy-to-azure.sh
```

2. Run the deployment script:
```bash
./deploy-to-azure.sh
```

3. Set up secrets after deployment:
```bash
az containerapp secret set --name video-srt-api --resource-group video-srt-rg --secrets azure-speech-key=YOUR_AZURE_SPEECH_KEY

az containerapp secret set --name video-srt-api --resource-group video-srt-rg --secrets azure-speech-region=YOUR_AZURE_REGION

az containerapp secret set --name video-srt-api --resource-group video-srt-rg --secrets azure-blob-connection=YOUR_BLOB_CONNECTION_STRING
```

### Manual Deployment

1. Create Azure Container Registry:
```bash
az acr create --resource-group video-srt-rg --name videosrtregistry --sku Basic --admin-enabled true
```

2. Build and push image:
```bash
az acr build --registry videosrtregistry --image video-srt-app:latest --file Dockerfile .
```

3. Create Container App:
```bash
az containerapp create \
  --name video-srt-api \
  --resource-group video-srt-rg \
  --environment video-srt-env \
  --image videosrtregistry.azurecr.io/video-srt-app:latest \
  --target-port 80 \
  --ingress external \
  --min-replicas 1 \
  --max-replicas 10
```

## Project Structure

```
video-srt-app/
├── app/
│   ├── main.py              # FastAPI application
│   ├── models.py            # Database models
│   ├── utils.py             # Processing utilities
│   └── worker.py            # Background job processor
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
├── docker-compose.yml      # Local development setup
├── deploy-to-azure.sh      # Azure deployment script
├── .env.example            # Environment variables template
└── README.md              # This file
```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AZURE_SPEECH_KEY` | Azure Speech Services subscription key | Yes |
| `AZURE_SPEECH_REGION` | Azure Speech Services region | Yes |
| `AZURE_BLOB_CONNECTION` | Azure Blob Storage connection string | Yes |
| `AZURE_BLOB_CONTAINER` | Blob container name for SRT files | No (default: subtitles) |
| `DATABASE_URL` | Database connection string | No (default: SQLite) |

### Database Options

- **SQLite** (default): `sqlite:///jobs.db`
- **PostgreSQL**: `postgresql://user:password@localhost/dbname`

## Error Handling

The API provides detailed error messages for common issues:

- **Invalid video URL**: Returns 400 with validation error
- **Missing Azure credentials**: Returns 500 with configuration error
- **Job not found**: Returns 404 with appropriate message
- **Processing failures**: Job status set to FAILED with error details

## Monitoring and Logging

- Health check endpoint: `/health`
- Structured logging throughout the application
- Job status tracking in database
- Error messages stored with failed jobs

## Performance Considerations

- **Stateless design**: Enables horizontal scaling
- **Background processing**: Non-blocking job submission
- **Resource management**: Automatic cleanup of temporary files
- **Container optimization**: Multi-stage builds for smaller images

## Troubleshooting

### Common Issues

1. **Azure Speech Services quota exceeded**
   - Check your Azure subscription limits
   - Monitor usage in Azure portal

2. **Video download fails**
   - Verify video URL is accessible
   - Check network connectivity

3. **Audio extraction fails**
   - Ensure video format is supported by MoviePy
   - Check available disk space

4. **Blob upload fails**
   - Verify Azure Blob Storage credentials
   - Check container permissions

### Debug Mode

For local development, enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section
- Review Azure documentation
- Create an issue in the repository