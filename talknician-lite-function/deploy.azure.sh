#!/bin/bash

# Quick Azure deployment script - skips local Docker build
# Uses Azure Container Registry build (much faster)

# Your configuration
RESOURCE_GROUP="talknician-lite-production"
LOCATION="centralus"
REGISTRY_NAME="talknicianlitebackend"
APP_NAME="talknician-video-to-srt-api"
CONTAINER_APP_ENV="talknician-video-to-srt-env"
IMAGE_NAME="talknician-video-to-srt-api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Quick Azure deployment for Video-to-SRT API${NC}"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if logged in to Azure
echo -e "${YELLOW}🔐 Checking Azure login...${NC}"
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}Please login to Azure:${NC}"
    az login
fi

# Check if resource group exists
echo -e "${YELLOW}📦 Checking resource group...${NC}"
if ! az group show --name $RESOURCE_GROUP > /dev/null 2>&1; then
    echo -e "${YELLOW}Creating resource group...${NC}"
    az group create --name $RESOURCE_GROUP --location $LOCATION
fi

# Check if registry exists
echo -e "${YELLOW}🏗️ Checking container registry...${NC}"
if ! az acr show --name $REGISTRY_NAME > /dev/null 2>&1; then
    echo -e "${YELLOW}Creating container registry...${NC}"
    az acr create \
        --resource-group $RESOURCE_GROUP \
        --name $REGISTRY_NAME \
        --sku Basic \
        --admin-enabled true
fi

# Get ACR login server
ACR_LOGIN_SERVER=$(az acr show --name $REGISTRY_NAME --query loginServer --output tsv)
echo -e "${GREEN}Registry: $ACR_LOGIN_SERVER${NC}"

# Build image directly in Azure (much faster than local build)
echo -e "${YELLOW}🔨 Building image in Azure Container Registry...${NC}"
az acr build \
    --registry $REGISTRY_NAME \
    --image $IMAGE_NAME:latest \
    --file Dockerfile \
    .

# Check if Container Apps environment exists
echo -e "${YELLOW}🌐 Checking Container Apps environment...${NC}"
if ! az containerapp env show --name $CONTAINER_APP_ENV --resource-group $RESOURCE_GROUP > /dev/null 2>&1; then
    echo -e "${YELLOW}Creating Container Apps environment...${NC}"
    az containerapp env create \
        --name $CONTAINER_APP_ENV \
        --resource-group $RESOURCE_GROUP \
        --location $LOCATION
fi

# Get ACR credentials
ACR_USERNAME=$(az acr credential show --name $REGISTRY_NAME --query username --output tsv)
ACR_PASSWORD=$(az acr credential show --name $REGISTRY_NAME --query passwords[0].value --output tsv)

# Check if container app exists
if az containerapp show --name $APP_NAME --resource-group $RESOURCE_GROUP > /dev/null 2>&1; then
    echo -e "${YELLOW}📱 Updating existing container app...${NC}"
    az containerapp update \
        --name $APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --image $ACR_LOGIN_SERVER/$IMAGE_NAME:latest
else
    echo -e "${YELLOW}📱 Creating new container app...${NC}"
    az containerapp create \
        --name $APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --environment $CONTAINER_APP_ENV \
        --image $ACR_LOGIN_SERVER/$IMAGE_NAME:latest \
        --registry-server $ACR_LOGIN_SERVER \
        --registry-username $ACR_USERNAME \
        --registry-password $ACR_PASSWORD \
        --target-port 80 \
        --ingress external \
        --min-replicas 1 \
        --max-replicas 5 \
        --cpu 1.0 \
        --memory 2.0Gi \
        --env-vars \
            "AZURE_BLOB_CONTAINER=subtitles" \
            "DATABASE_URL=sqlite:///jobs.db"
fi

# Get the application URL
APP_URL=$(az containerapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query properties.configuration.ingress.fqdn --output tsv)

echo -e "${GREEN}✅ Deployment completed!${NC}"
echo -e "${GREEN}🌐 Application URL: https://$APP_URL${NC}"
echo -e "${GREEN}📊 Health check: https://$APP_URL/health${NC}"
echo -e "${GREEN}📖 API docs: https://$APP_URL/docs${NC}"

echo -e "${YELLOW}⚠️  Next steps - Set up secrets:${NC}"
echo -e "${YELLOW}1. Set Azure Speech key:${NC}"
echo "az containerapp secret set --name $APP_NAME --resource-group $RESOURCE_GROUP --secrets azure-speech-key=YOUR_AZURE_SPEECH_KEY"

echo -e "${YELLOW}2. Set Azure Speech region:${NC}"
echo "az containerapp secret set --name $APP_NAME --resource-group $RESOURCE_GROUP --secrets azure-speech-region=YOUR_AZURE_REGION"

echo -e "${YELLOW}3. Set Azure Blob connection:${NC}"
echo "az containerapp secret set --name $APP_NAME --resource-group $RESOURCE_GROUP --secrets azure-blob-connection=\"YOUR_BLOB_CONNECTION_STRING\""

echo -e "${YELLOW}4. Update container app with secrets:${NC}"
echo "az containerapp update --name $APP_NAME --resource-group $RESOURCE_GROUP --set-env-vars AZURE_SPEECH_KEY=secretref:azure-speech-key AZURE_SPEECH_REGION=secretref:azure-speech-region AZURE_BLOB_CONNECTION=secretref:azure-blob-connection"

echo -e "${GREEN}🎉 Quick deployment completed!${NC}"