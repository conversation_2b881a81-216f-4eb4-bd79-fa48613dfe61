// Chat-related types for Houston AI Chat

import { MessageDocument } from "@/api/message-document";
import { UploadedFileState } from "@/components/houston/ChatInput";

export interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  conversationId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
  annotations?: MessageAnnotation[];
  references?: MessageReference[];
  openaiMessageId?: string;
  openaiItemId?: string;
  sequenceNumber?: number;
  metadata?: any;
  messageDocuments?: UploadedFileState[];
}

export interface MessageAnnotation {
  id: string;
  type: "url_citation" | "file_citation";
  startIndex: number;
  endIndex: number;
  url?: string;
  title?: string;
  fileId?: string;
  filename?: string;
  documentId?: string;
  websiteId?: string;
}

export interface MessageReference {
  id: string;
  type: "DOCUMENT" | "WEBSITE";
  title: string;
  documentId?: string;
  azureBlobUrl?: string;
  openaiFileId?: string;
  filename?: string;
  websiteId?: string;
  url?: string;
  scrapedContent?: string;
  metadata?: any;
  createdAt: string;
}

export type ConversationVisibility = "private" | "public";

export interface ConversationUser {
  id: string;
  name: string | null;
  email: string;
  avatar?: string | null;
}

export interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  userId: string;
  visibility: ConversationVisibility;
  user?: ConversationUser;
  messages?: ChatMessage[];
}

export interface ChatSettings {
  documentSearch?: boolean;
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
}

export interface ChatPersona {
  id: string;
  title: string;
  systemPrompt: string;
  isPublic: boolean;
  isDefault: boolean;
  isOwned: boolean;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string | null;
    email: string;
  } | null;
}

export interface StreamingState {
  isStreaming: boolean;
  currentContent: string;
  error: string | null;
  status: string;
  loadingMessage: string;
}

// Conversation filtering types
export type ConversationFilter = "my-chats" | "public-chats" | "all";

// API Response types
export interface ConversationsResponse {
  success: boolean;
  data: {
    conversations: Conversation[];
  };
}

export interface ConversationResponse {
  success: boolean;
  data: {
    conversation: Conversation;
    messages: ChatMessage[];
  };
}

export interface ConversationVisibilityUpdateRequest {
  visibility: ConversationVisibility;
}

export interface ConversationVisibilityUpdateResponse {
  success: boolean;
  data: {
    conversation: Conversation;
  };
}

export interface CreateConversationResponse {
  success: boolean;
  data: Conversation;
}

export interface ChatSettingsResponse {
  success: boolean;
  data: ChatSettings;
}
