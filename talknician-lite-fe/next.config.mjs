/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_PUSHER_KEY: "a4bc47e4fd8869f9ca3b",
    NEXT_PUBLIC_PUSHER_CLUSTER: "ap1",
  },
  experimental: {
    // This is experimental and may change or be removed at any time.
    serverComponentsExternalPackages: ["@prisma/client", "bcrypt"],
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "litedevelopment.blob.core.windows.net",
      },
      {
        protocol: "https",
        hostname: "talknicianliteproduction.blob.core.windows.net",
      },
    ],
  },
};

export default nextConfig;
