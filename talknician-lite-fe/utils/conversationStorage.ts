// Utility functions for managing conversation state in localStorage

const LAST_CONVERSATION_KEY = 'houston_last_conversation';
const CONVERSATION_SETTINGS_PREFIX = 'houston_conversation_settings_';

export interface ConversationStorageSettings {
  personaId: string | null;
  fileSearch: boolean;
  webSearch: boolean;
}

/**
 * Get the last accessed conversation ID from localStorage
 */
export function getLastConversationId(): string | null {
  if (typeof window === 'undefined') return null;
  
  try {
    return localStorage.getItem(LAST_CONVERSATION_KEY);
  } catch (error) {
    console.warn('Failed to get last conversation ID from localStorage:', error);
    return null;
  }
}

/**
 * Save the last accessed conversation ID to localStorage
 */
export function setLastConversationId(conversationId: string | null): void {
  if (typeof window === 'undefined') return;
  
  try {
    if (conversationId) {
      localStorage.setItem(LAST_CONVERSATION_KEY, conversationId);
    } else {
      localStorage.removeItem(LAST_CONVERSATION_KEY);
    }
  } catch (error) {
    console.warn('Failed to save last conversation ID to localStorage:', error);
  }
}

/**
 * Get conversation settings from localStorage
 */
export function getConversationSettings(conversationId: string): ConversationStorageSettings | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem(`${CONVERSATION_SETTINGS_PREFIX}${conversationId}`);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.warn('Failed to get conversation settings from localStorage:', error);
    return null;
  }
}

/**
 * Save conversation settings to localStorage
 */
export function setConversationSettings(
  conversationId: string, 
  settings: ConversationStorageSettings
): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(
      `${CONVERSATION_SETTINGS_PREFIX}${conversationId}`, 
      JSON.stringify(settings)
    );
  } catch (error) {
    console.warn('Failed to save conversation settings to localStorage:', error);
  }
}

/**
 * Remove conversation settings from localStorage
 */
export function removeConversationSettings(conversationId: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(`${CONVERSATION_SETTINGS_PREFIX}${conversationId}`);
  } catch (error) {
    console.warn('Failed to remove conversation settings from localStorage:', error);
  }
}

/**
 * Clear all conversation-related data from localStorage
 */
export function clearAllConversationData(): void {
  if (typeof window === 'undefined') return;
  
  try {
    // Remove last conversation ID
    localStorage.removeItem(LAST_CONVERSATION_KEY);
    
    // Remove all conversation settings
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(CONVERSATION_SETTINGS_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Failed to clear conversation data from localStorage:', error);
  }
}
