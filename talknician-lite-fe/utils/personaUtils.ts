// LocalStorage keys for conversation-persona mapping
const CONVERSATION_PERSONA_STORAGE_KEY = "talknician-conversation-personas";
const LAST_PERSONA_STORAGE_KEY = "talknician-last-persona";

// New: Conversation settings (personaId, fileSearch, webSearch)
const CONVERSATION_SETTINGS_STORAGE_KEY = "talknician-conversation-settings";

export interface ConversationSettings {
  personaId: string | null;
  fileSearch: boolean;
  webSearch: boolean;
}

// Conversation-specific persona selection
export function setConversationPersona(
  conversationId: string,
  personaId: string
): void {
  try {
    const conversationPersonas = getConversationPersonas();
    conversationPersonas[conversationId] = personaId;
    localStorage.setItem(
      CONVERSATION_PERSONA_STORAGE_KEY,
      JSON.stringify(conversationPersonas)
    );
  } catch (error) {
    console.error("Error saving conversation persona to localStorage:", error);
  }
}

export function getConversationPersona(conversationId: string): string | null {
  try {
    const conversationPersonas = getConversationPersonas();
    return conversationPersonas[conversationId] || null;
  } catch (error) {
    console.error(
      "Error loading conversation persona from localStorage:",
      error
    );
    return null;
  }
}

export function removeConversationPersona(conversationId: string): void {
  try {
    const conversationPersonas = getConversationPersonas();
    delete conversationPersonas[conversationId];
    localStorage.setItem(
      CONVERSATION_PERSONA_STORAGE_KEY,
      JSON.stringify(conversationPersonas)
    );
  } catch (error) {
    console.error(
      "Error removing conversation persona from localStorage:",
      error
    );
  }
}

function getConversationPersonas(): Record<string, string> {
  try {
    const stored = localStorage.getItem(CONVERSATION_PERSONA_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error(
      "Error loading conversation personas from localStorage:",
      error
    );
    return {};
  }
}

// Last selected persona management
export function setLastPersona(personaId: string): void {
  try {
    localStorage.setItem(LAST_PERSONA_STORAGE_KEY, personaId);
  } catch (error) {
    console.error("Error saving last persona to localStorage:", error);
  }
}

export function getLastPersona(): string | null {
  try {
    return localStorage.getItem(LAST_PERSONA_STORAGE_KEY);
  } catch (error) {
    console.error("Error loading last persona from localStorage:", error);
    return null;
  }
}

// New: Conversation settings (personaId, fileSearch, webSearch)
export function setConversationSettings(
  conversationId: string,
  settings: ConversationSettings
): void {
  try {
    const allSettings = getAllConversationSettings();
    allSettings[conversationId] = settings;
    localStorage.setItem(
      CONVERSATION_SETTINGS_STORAGE_KEY,
      JSON.stringify(allSettings)
    );
  } catch (error) {
    console.error("Error saving conversation settings to localStorage:", error);
  }
}

export function getConversationSettings(
  conversationId: string
): ConversationSettings | null {
  try {
    const allSettings = getAllConversationSettings();
    return allSettings[conversationId] || null;
  } catch (error) {
    console.error(
      "Error loading conversation settings from localStorage:",
      error
    );
    return null;
  }
}

export function removeConversationSettings(conversationId: string): void {
  try {
    const allSettings = getAllConversationSettings();
    delete allSettings[conversationId];
    localStorage.setItem(
      CONVERSATION_SETTINGS_STORAGE_KEY,
      JSON.stringify(allSettings)
    );
  } catch (error) {
    console.error(
      "Error removing conversation settings from localStorage:",
      error
    );
  }
}

function getAllConversationSettings(): Record<string, ConversationSettings> {
  try {
    const stored = localStorage.getItem(CONVERSATION_SETTINGS_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error(
      "Error loading conversation settings from localStorage:",
      error
    );
    return {};
  }
}
