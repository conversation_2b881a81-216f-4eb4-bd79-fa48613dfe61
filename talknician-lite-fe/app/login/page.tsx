import { Suspense } from "react";
import LoginForm from "./LoginForm";
import { Skeleton } from "@/components/ui/skeleton";

function LoginLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-3 sm:p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-6 sm:mb-8">
          <div className="inline-flex items-center space-x-3">
            <Skeleton className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl" />
            <Skeleton className="h-7 w-32" />
          </div>
          <Skeleton className="h-5 w-64 mt-2 mx-auto" />
        </div>

        <div className="flex justify-center mb-4 sm:mb-6">
          <Skeleton className="w-20 h-10" />
        </div>

        <div className="border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800 rounded-lg p-4 sm:p-6 space-y-4">
          <div className="text-center">
            <Skeleton className="h-7 w-24 mx-auto" />
            <Skeleton className="h-4 w-48 mx-auto mt-2" />
          </div>
          <div className="flex rounded-lg bg-slate-100 dark:bg-slate-700 p-1">
            <Skeleton className="h-10 flex-1 rounded-md" />
            <Skeleton className="h-10 flex-1 rounded-md" />
          </div>
          <Skeleton className="h-11 sm:h-12 w-full" />
          <Skeleton className="h-11 sm:h-12 w-full" />
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginForm />
    </Suspense>
  );
}
