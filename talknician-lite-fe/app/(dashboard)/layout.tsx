import type React from "react";
import { AuthGuard } from "@/components/auth-guard";
import { OrganizationRequiredModal } from "@/components/organization-required-modal";
import { SidebarNavigation } from "@/components/sidebar-navigation";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import { PusherListener } from "@/components/pusher-listener";
import { OrganizationRefreshListener } from "@/components/organization-refresh-listener";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <OrganizationProvider>
        <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
          <div className="flex-shrink-0">
            <SidebarNavigation />
          </div>
          <main className="flex-1 overflow-hidden md:ml-0">
            <div className="h-full pt-16 md:pt-0">{children}</div>
          </main>
          <PusherListener />
          <OrganizationRefreshListener />
        </div>
      </OrganizationProvider>
    </AuthGuard>
  );
}
