import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageCircle, ArrowLeft } from "lucide-react";

export default function ConversationNotFound() {
  return (
    <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-4">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
              <MessageCircle className="w-8 h-8 text-slate-400" />
            </div>
          </div>
          
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold text-slate-900 dark:text-white">
              Conversation Not Found
            </h1>
            <p className="text-slate-600 dark:text-slate-400">
              The conversation you're looking for doesn't exist or you don't have permission to view it.
            </p>
          </div>

          <div className="space-y-3">
            <Button asChild className="w-full">
              <Link href="/houston/new">
                <MessageCircle className="w-4 h-4 mr-2" />
                Start New Conversation
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/houston">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Houston
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
