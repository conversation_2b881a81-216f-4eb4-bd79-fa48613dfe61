"use client";

import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { useHoustonChatSSE } from "@/hooks/useHoustonChatSSE";
import { usePersonas } from "@/hooks/usePersonas";
import { useSettingsStore } from "@/stores/useSettingsStore";
import {
  getConversationSettings,
  setConversationSettings,
} from "@/utils/personaUtils";
import { ConversationsSidebar } from "@/components/houston/ConversationsSidebar";
import { ChatHeader } from "@/components/houston/ChatHeader";
import { MessagesList } from "@/components/houston/MessagesList";
import { ChatInput, UploadedFileState } from "@/components/houston/ChatInput";
import { SettingsDialog } from "@/components/houston/SettingsDialog";

export default function HoustonPage() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isMobileConversationsOpen, setIsMobileConversationsOpen] =
    useState(false);
  const [conversationSearchQuery, setConversationSearchQuery] = useState("");
  const { personas, getPersona, refreshPersonas } = usePersonas();
  const [selectedPersonaId, setSelectedPersonaId] = useState<string | null>(
    null
  );

  const {
    currentRequestWebSearch,
    currentRequestFileSearch,
    setCurrentRequestWebSearch,
    setCurrentRequestFileSearch,
  } = useSettingsStore();

  const currentPersona = getPersona(selectedPersonaId);

  // Use the integrated hook that combines SSE, Zustand, and React Query
  const {
    // State
    currentConversation,
    messages,
    conversations,
    streamingState,

    // Loading states
    isLoadingConversations,

    // Actions
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    stopStreaming,
    renameConversation,
  } = useHoustonChatSSE();

  const handleSelectConversation = async (conversationId: string) => {
    const savedSettings = await loadConversation(conversationId);
    if (savedSettings) {
      setSelectedPersonaId(savedSettings.personaId);
      setCurrentRequestFileSearch(savedSettings.fileSearch);
      setCurrentRequestWebSearch(savedSettings.webSearch);
    } else {
      setSelectedPersonaId(null);
      setCurrentRequestFileSearch(false);
      setCurrentRequestWebSearch(false);
    }
  };

  const handlePersonaSelect = (personaId: string | null) => {
    setSelectedPersonaId(personaId);
    if (currentConversation) {
      setConversationSettings(currentConversation.id, {
        personaId: personaId,
        fileSearch: currentRequestFileSearch,
        webSearch: currentRequestWebSearch,
      });
    }
  };

  useEffect(() => {
    if (currentConversation) {
      setConversationSettings(currentConversation.id, {
        personaId: selectedPersonaId,
        fileSearch: currentRequestFileSearch,
        webSearch: currentRequestWebSearch,
      });
    }
  }, [
    currentConversation,
    selectedPersonaId,
    currentRequestFileSearch,
    currentRequestWebSearch,
  ]);

  const handleSendMessage = async (
    message: string,
    files?: UploadedFileState[]
  ) => {
    const settings = {
      personality: currentPersona?.systemPrompt,
      documentSearch: true,
      enableWebSearch: currentRequestWebSearch,
    };

    if (!currentConversation) {
      // Create new conversation first, then send message
      createNewConversation(message, settings);
      return;
    }

    await sendMessage({
      conversationId: currentConversation.id,
      message,
      settings,
      files,
    });
  };

  return (
    <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-80 md:flex-col">
        <ConversationsSidebar
          conversations={conversations}
          currentConversation={currentConversation}
          conversationSearchQuery={conversationSearchQuery}
          isLoading={isLoadingConversations}
          onSearchChange={setConversationSearchQuery}
          onConversationSelect={handleSelectConversation}
          onCreateNew={() => createNewConversation(undefined, {})}
          onDeleteConversation={deleteConversation}
          onRenameConversation={renameConversation}
        />
      </div>

      {/* Mobile Sidebar */}
      {isMobileConversationsOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsMobileConversationsOpen(false)}
          />
          <div className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-slate-800">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Conversations</h2>
              <button
                onClick={() => setIsMobileConversationsOpen(false)}
                className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <ConversationsSidebar
              conversations={conversations}
              currentConversation={currentConversation}
              conversationSearchQuery={conversationSearchQuery}
              isLoading={isLoadingConversations}
              onSearchChange={setConversationSearchQuery}
              onConversationSelect={(id) => {
                handleSelectConversation(id);
                setIsMobileConversationsOpen(false);
              }}
              onCreateNew={() => {
                createNewConversation(undefined, {});
                setIsMobileConversationsOpen(false);
              }}
              onDeleteConversation={deleteConversation}
              onRenameConversation={renameConversation}
            />
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        <ChatHeader
          currentConversation={currentConversation}
          streamingState={streamingState}
          onMobileMenuToggle={() => setIsMobileConversationsOpen(true)}
          onSettingsClick={() => setIsSettingsOpen(true)}
          onStopStreaming={stopStreaming}
          currentPersona={currentPersona}
        />

        <MessagesList
          messages={messages}
          streamingState={streamingState}
          className="flex-1"
        />

        <ChatInput
          onSend={handleSendMessage}
          onStop={stopStreaming}
          isStreaming={streamingState.isStreaming}
          webSearchEnabled={currentRequestWebSearch}
          onWebSearchToggle={setCurrentRequestWebSearch}
          fileSearchEnabled={currentRequestFileSearch}
          onFileSearchToggle={setCurrentRequestFileSearch}
        />
      </div>

      {/* Settings Dialog */}
      <SettingsDialog
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        selectedPersonaId={selectedPersonaId}
        onPersonaSelect={handlePersonaSelect}
        personas={personas}
        refreshPersonas={refreshPersonas}
      />
    </div>
  );
}
