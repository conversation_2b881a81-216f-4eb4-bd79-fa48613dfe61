"use client";

import { useState, useRef } from "react";
import { useUploadDocumentMutation } from "@/api/hooks";
import { useOrganization } from "@/contexts/OrganizationContext";
import { toast } from "sonner";
import { getSupportedFileTypes } from "../../utils/fileUtils";

export function useFileUpload() {
  const { currentOrganization } = useOrganization();
  const [uploadingFiles, setUploadingFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadMutation = useUploadDocumentMutation();

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    if (!currentOrganization) {
      toast.error("Please select an organization first");
      return;
    }

    setUploadingFiles(files);

    // Upload files one by one
    for (const file of files) {
      try {
        await uploadMutation.mutateAsync({
          file,
          organizationId: currentOrganization.id,
        });

        toast.success(`${file.name} uploaded successfully`);
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error(`Failed to upload ${file.name}`);
      }
    }

    setUploadingFiles([]);
    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return {
    uploadingFiles,
    fileInputRef,
    isUploading: uploadMutation.isPending,
    handleFileUpload,
    handleFileSelect,
    supportedFileTypes: getSupportedFileTypes(),
  };
}
