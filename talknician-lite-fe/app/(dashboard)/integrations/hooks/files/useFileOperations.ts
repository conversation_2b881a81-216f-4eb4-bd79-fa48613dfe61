"use client";

import { useState } from "react";
import {
  useDeleteDocumentMutation,
  useAddDocumentToRAGMutation,
  useRemoveDocumentFromRAGMutation,
  useRenameDocumentMutation,
} from "@/api/hooks";
import { useDocumentApi, Document } from "@/api/documents";
import { toast } from "sonner";
import { RenameModalState, DocumentModalState } from "../../types/files";

export function useFileOperations() {
  const deleteMutation = useDeleteDocumentMutation();
  const addToRAGMutation = useAddDocumentToRAGMutation();
  const removeFromRAGMutation = useRemoveDocumentFromRAGMutation();
  const renameMutation = useRenameDocumentMutation();
  const apiClient = useDocumentApi();

  const [renameModal, setRenameModal] = useState<RenameModalState>({
    open: false,
    file: null,
    value: "",
    error: "",
  });

  const [docModal, setDocModal] = useState<DocumentModalState>({
    open: false,
    url: "",
    title: "",
    filename: "",
  });

  const handleAddToRAG = async (documentId: string) => {
    try {
      await addToRAGMutation.mutateAsync(documentId);
      toast.success("Document added to Houston successfully");
    } catch (error) {
      console.error("Error adding to RAG:", error);
      toast.error("Failed to add document to Houston");
    }
  };

  const handleRemoveFromRAG = async (documentId: string) => {
    try {
      await removeFromRAGMutation.mutateAsync(documentId);
      toast.success("Document removed from Houston");
    } catch (error) {
      console.error("Error removing from RAG:", error);
      toast.error("Failed to remove document from Houston");
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await deleteMutation.mutateAsync(documentId);
      toast.success("Document deleted successfully");
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("Failed to delete document");
    }
  };

  const handleDownloadDocument = async (fileId: string) => {
    try {
      const { blob, filename } = await apiClient.downloadDocument(fileId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Failed to download file");
    }
  };

  const openRenameModal = (file: Document) => {
    setRenameModal({
      open: true,
      file,
      value: file.originalName,
      error: "",
    });
  };

  const confirmRename = async () => {
    if (!renameModal.file) return;
    if (!renameModal.value.trim()) {
      setRenameModal((prev) => ({
        ...prev,
        error: "File name cannot be empty",
      }));
      return;
    }
    if (renameModal.value === renameModal.file.originalName) {
      setRenameModal((prev) => ({ ...prev, open: false }));
      return;
    }
    try {
      await renameMutation.mutateAsync({
        id: renameModal.file.id,
        originalName: renameModal.value,
        organizationId: renameModal.file.organizationId,
      });
      toast.success("File renamed successfully");
      setRenameModal({ open: false, file: null, value: "", error: "" });
    } catch (error) {
      setRenameModal((prev) => ({ ...prev, error: "Failed to rename file" }));
    }
  };

  const openDocumentModal = (file: Document) => {
    let url = "";
    if (file.azureBlobUrl) url = file.azureBlobUrl;
    setDocModal({
      open: true,
      url,
      title: file.originalName || "Document",
      filename: file.originalName || "",
    });
  };

  return {
    // Mutations
    deleteMutation,
    addToRAGMutation,
    removeFromRAGMutation,
    renameMutation,

    // Modal states
    renameModal,
    setRenameModal,
    docModal,
    setDocModal,

    // Actions
    handleAddToRAG,
    handleRemoveFromRAG,
    handleDeleteDocument,
    handleDownloadDocument,
    openRenameModal,
    confirmRename,
    openDocumentModal,
  };
}
