"use client";

import { useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useOneDriveStore } from "@/stores/useOneDriveStore";
import { useOneDriveApi } from "@/api/onedrive";

export function useOneDrive() {
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  const oneDriveApi = useOneDriveApi();
  const {
    path,
    items,
    history,
    loading,
    fileStatuses,
    setFileStatus,
    navigateToFolder: storeNavigateToFolder,
    goBack,
  } = useOneDriveStore();

  const navigateToFolder = useCallback(
    (newPath: string) => {
      storeNavigateToFolder(newPath, currentOrganization?.id, accessToken);
    },
    [storeNavigateToFolder, currentOrganization?.id, accessToken]
  );

  const addFileToRAG = async (fileId: string, fileName: string) => {
    if (!currentOrganization) return;

    try {
      setFileStatus(fileId, "Processing");
      await oneDriveApi.addOneDriveFileToRAG({
        fileId,
        fileName,
        organizationId: currentOrganization.id,
      });
      setFileStatus(fileId, "Added");
    } catch (error) {
      console.error("Error adding file to RAG:", error);
      setFileStatus(fileId, "Error");
    }
  };

  return {
    // State
    path,
    items,
    history,
    loading,
    fileStatuses,
    // Actions
    navigateToFolder,
    goBack,
    addFileToRAG,
    // API
    oneDriveApi,
  };
}
