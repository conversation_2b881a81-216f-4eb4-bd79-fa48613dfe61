import React from "react";
import { FileText, ImageIcon, File } from "lucide-react";

export const getFileIcon = (filename: string): React.ReactElement => {
  const ext = filename.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "pdf":
      return React.createElement(FileText, {
        className: "w-5 h-5 text-red-500",
      });
    case "doc":
    case "docx":
      return React.createElement(FileText, {
        className: "w-5 h-5 text-blue-500",
      });
    case "png":
    case "jpg":
    case "jpeg":
    case "gif":
      return React.createElement(ImageIcon, {
        className: "w-5 h-5 text-green-500",
      });
    case "txt":
    case "md":
      return React.createElement(File, { className: "w-5 h-5 text-gray-500" });
    default:
      return React.createElement(File, { className: "w-5 h-5 text-gray-500" });
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const getSupportedFileTypes = (): string => {
  return ".pdf,.docx,.doc,.txt,.md,.csv,.xlsx,.xls,.pptx,.ppt";
};
