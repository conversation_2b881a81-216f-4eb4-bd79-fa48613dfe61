"use client";

import { useState } from "react";
import { List, Grid3X3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { TabsContent } from "@/components/ui/tabs";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useDocumentsQuery } from "@/api/hooks";
import { ViewMode } from "../../types/files";
import { useFileOperations } from "../../hooks/files/useFileOperations";
import FileUpload from "./FileUpload";
import FileList from "./FileList";
import RenameModal from "./RenameModal";
import DocumentModal from "@/components/houston/DocumentModal";

interface FilesTabProps {
  searchQuery: string;
}

export default function FilesTab({ searchQuery }: FilesTabProps) {
  const { currentOrganization } = useOrganization();
  const [viewMode, setViewMode] = useState<ViewMode>("list");

  const { data: documentsResponse, isLoading: documentsLoading } =
    useDocumentsQuery(currentOrganization?.id || "");

  const {
    renameModal,
    setRenameModal,
    docModal,
    setDocModal,
    renameMutation,
    confirmRename,
  } = useFileOperations();

  const documents = documentsResponse?.data || [];

  const filteredDocuments = documents.filter((doc) =>
    doc.originalName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <TabsContent value="files" className="mt-0 h-full">
      <div className="flex flex-col h-full">
        {/* Toolbar */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0 w-full">
          <div className="flex items-center space-x-2 w-full">
            <FileUpload />
          </div>
        </div>

        {/* File List */}
        <FileList
          documents={filteredDocuments}
          loading={documentsLoading}
          viewMode={viewMode}
        />

        {/* Modals */}
        <RenameModal
          renameModal={renameModal}
          onValueChange={(value) =>
            setRenameModal((prev) => ({ ...prev, value, error: "" }))
          }
          onConfirm={confirmRename}
          onClose={() =>
            setRenameModal({ open: false, file: null, value: "", error: "" })
          }
          isLoading={renameMutation.isPending}
        />

        <DocumentModal
          open={docModal.open}
          onOpenChange={(open) => setDocModal((prev) => ({ ...prev, open }))}
          documentUrl={docModal.url}
          title={docModal.title}
          filename={docModal.filename}
        />
      </div>
    </TabsContent>
  );
}
