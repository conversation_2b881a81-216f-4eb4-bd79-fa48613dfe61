"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { RenameModalState } from "../../types/files";

interface RenameModalProps {
  renameModal: RenameModalState;
  onValueChange: (value: string) => void;
  onConfirm: () => void;
  onClose: () => void;
  isLoading: boolean;
}

export default function RenameModal({
  renameModal,
  onValueChange,
  onConfirm,
  onClose,
  isLoading,
}: RenameModalProps) {
  return (
    <Dialog open={renameModal.open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Rename File</DialogTitle>
          <DialogDescription>Enter a new name for your file.</DialogDescription>
        </DialogHeader>
        <Input
          value={renameModal.value}
          onChange={(e) => onValueChange(e.target.value)}
          autoFocus
          disabled={isLoading}
          onKeyDown={(e) => {
            if (e.key === "Enter") onConfirm();
          }}
        />
        {renameModal.error && (
          <div className="text-red-600 text-sm mt-1">{renameModal.error}</div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={onConfirm} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
