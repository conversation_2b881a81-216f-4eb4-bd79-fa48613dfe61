"use client";

import {
  MoreVertical,
  Download,
  Edit,
  Trash2,
  <PERSON><PERSON>ircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Document } from "@/api/documents";
import { ViewMode } from "../../types/files";
import { getFileIcon, formatFileSize } from "../../utils/fileUtils";
import { formatDate } from "../../utils/dateUtils";
import { useFileOperations } from "../../hooks/files/useFileOperations";

interface FileItemProps {
  file: Document;
  viewMode: ViewMode;
}

export default function FileItem({ file, viewMode }: FileItemProps) {
  const {
    handleAddToRAG,
    handleRemoveFromRAG,
    handleDeleteDocument,
    handleDownloadDocument,
    openRenameModal,
    openDocumentModal,
  } = useFileOperations();

  const handleFileClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    openDocumentModal(file);
  };

  const renderRAGStatus = () => {
    if (file.status === "PROCESSING") {
      return (
        <Badge
          variant="outline"
          className="border-yellow-200 text-yellow-700 bg-yellow-50"
        >
          <Loader2 className="w-3 h-3 mr-1 animate-spin" />
          Processing
        </Badge>
      );
    }

    if (file.status === "FAILED") {
      return (
        <Badge
          variant="outline"
          className="border-red-200 text-red-700 bg-red-50"
        >
          <AlertCircle className="w-3 h-3 mr-1" />
          Failed
        </Badge>
      );
    }

    if (file.inRAG) {
      return (
        <Button
          size="sm"
          variant="default"
          onClick={(e) => {
            e.stopPropagation();
            handleRemoveFromRAG(file.id);
          }}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          <CheckCircle className="w-3 h-3 mr-1" />
          In Houston
        </Button>
      );
    }

    return (
      <Button
        size="sm"
        variant="outline"
        onClick={(e) => {
          e.stopPropagation();
          handleAddToRAG(file.id);
        }}
        className="border-slate-300 hover:bg-slate-50 hover:text-slate-900"
      >
        Add to Houston
      </Button>
    );
  };

  const renderDropdownMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreVertical className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleDownloadDocument(file.id);
          }}
        >
          <Download className="w-4 h-4 mr-2" />
          Download
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            openRenameModal(file);
          }}
        >
          <Edit className="w-4 h-4 mr-2" />
          Rename
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="text-red-600"
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteDocument(file.id);
          }}
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Mobile Layout
  if (viewMode === "grid" || window.innerWidth < 640) {
    return (
      <div
        className="sm:hidden p-4 border-b border-slate-100 dark:border-slate-700 cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700"
        onClick={handleFileClick}
      >
        <div className="flex items-start space-x-3">
          <div className="mt-1">{getFileIcon(file.originalName)}</div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-slate-900 dark:text-white truncate">
              {file.originalName}
            </h4>
            <div className="flex items-center space-x-4 mt-1 text-xs text-slate-500 dark:text-slate-400">
              <span>{formatFileSize(file.size)}</span>
              <span>{formatDate(file.createdAt)}</span>
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center space-x-2">
                {renderRAGStatus()}
              </div>
              {renderDropdownMenu()}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div
      className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer"
      onClick={handleFileClick}
    >
      <div className="col-span-3 flex items-center space-x-3">
        {getFileIcon(file.originalName)}
        <span className="text-sm text-slate-900 dark:text-white truncate">
          {file.originalName}
        </span>
      </div>
      <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
        {file.author || "—"}
      </div>
      <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
        {formatFileSize(file.size)}
      </div>
      <div className="col-span-2 text-sm text-slate-500 dark:text-slate-400">
        {formatDate(file.createdAt)}
      </div>
      <div className="col-span-2">{renderRAGStatus()}</div>
      <div className="col-span-1 flex justify-end">{renderDropdownMenu()}</div>
    </div>
  );
}
