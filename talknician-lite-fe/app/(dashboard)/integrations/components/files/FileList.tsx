"use client";

import { Loader2, FileText, Upload } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Document } from "@/api/documents";
import { ViewMode } from "../../types/files";
import { useFileUpload } from "../../hooks/files/useFileUpload";
import FileItem from "./FileItem";

interface FileListProps {
  documents: Document[];
  loading: boolean;
  viewMode: ViewMode;
}

export default function FileList({
  documents,
  loading,
  viewMode,
}: FileListProps) {
  const { handleFileUpload } = useFileUpload();

  if (loading) {
    return (
      <div className="flex-1 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
          <span className="text-slate-500">Loading documents...</span>
        </div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="flex-1 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex items-center justify-center">
        <div className="text-center py-16">
          <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
            No documents yet
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            Upload your first document to get started
          </p>
          <Button
            onClick={handleFileUpload}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex flex-col">
      {/* Desktop Header */}
      <div className="hidden sm:grid grid-cols-12 gap-4 p-3 border-b border-slate-200 dark:border-slate-700 text-sm font-medium text-slate-500 dark:text-slate-400">
        <div className="col-span-3">Name</div>
        <div className="col-span-2">Author</div>
        <div className="col-span-2">Size</div>
        <div className="col-span-2">Modified</div>
        <div className="col-span-2">RAG Status</div>
        <div className="col-span-1"></div>
      </div>

      <ScrollArea className="flex-1 min-h-0">
        <div className="min-h-full">
          {documents.map((file) => (
            <FileItem key={file.id} file={file} viewMode={viewMode} />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
