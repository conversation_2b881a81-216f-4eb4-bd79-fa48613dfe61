"use client";

import { Upload, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useFileUpload } from "../../hooks/files/useFileUpload";

export default function FileUpload() {
  const {
    uploadingFiles,
    fileInputRef,
    isUploading,
    handleFileUpload,
    handleFileSelect,
    supportedFileTypes,
  } = useFileUpload();

  return (
    <div className="w-full sm:w-full">
      <Button
        onClick={handleFileUpload}
        disabled={isUploading}
        className="w-full sm:w-auto text-white"
      >
        <Upload className="w-4 h-4 mr-2" />
        Upload Files
      </Button>
      <input
        ref={fileInputRef}
        type="file"
        multiple
        style={{ display: "none" }}
        onChange={handleFileSelect}
        accept={supportedFileTypes}
        className="hidden"
      />
      {isUploading && (
        <div className="mt-4 mb-4 p-2 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 px-4 pt-2">
            Uploading Files...
          </h4>
          <div className="grid gap-2">
            {uploadingFiles.map((file) => (
              <div
                key={file.name}
                className="flex items-center space-x-2 text-sm text-blue-700 dark:text-blue-300"
              >
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>{file.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
