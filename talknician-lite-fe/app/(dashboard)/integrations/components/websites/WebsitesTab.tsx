"use client";

import { <PERSON>, Clock } from "lucide-react";
import { TabsContent } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function WebsitesTab() {
  return (
    <TabsContent value="websites" className="mt-0 h-full">
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Website Integrations
          </h2>
          <p className="text-slate-600 dark:text-slate-400">
            Connect and scrape content from websites for your knowledge base
          </p>
        </div>

        <div className="">
          <Card className="transition-all duration-200 hover:shadow-lg opacity-75">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Globe className="w-5 h-5 text-green-600" />
                </div>
                <span>Website Scraping</span>
                <Badge variant="secondary" className="ml-auto">
                  <Clock className="w-3 h-3 mr-1" />
                  Coming Soon
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Automatically extract and index content from websites to enhance
                your AI knowledge base.
              </p>

              <div className="text-center py-4">
                <p className="text-sm text-slate-500">
                  Website scraping integration is coming soon! Stay tuned for
                  updates.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </TabsContent>
  );
}
