"use client";

import { <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function SharePointCard() {
  return (
    <Card className="transition-all duration-200 hover:shadow-lg opacity-75">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Cloud className="w-5 h-5 text-blue-600" />
          </div>
          <span>SharePoint</span>
          <Badge variant="secondary" className="ml-auto">
            <Clock className="w-3 h-3 mr-1" />
            Coming Soon
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Connect your SharePoint to access and manage documents directly from
          Talknician.
        </p>

        <div className="text-center py-4">
          <p className="text-sm text-slate-500">
            SharePoint integration is coming soon! Stay tuned for updates.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
