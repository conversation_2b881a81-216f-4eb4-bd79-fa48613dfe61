"use client";

import { ChevronRight, Home } from "lucide-react";

interface OneDriveBreadcrumbsProps {
  path: string;
}

export default function OneDriveBreadcrumbs({
  path,
}: OneDriveBreadcrumbsProps) {
  if (path === "root") {
    return (
      <div className="flex items-center space-x-1 text-sm text-slate-600">
        <Home className="w-4 h-4" />
        <span>OneDrive</span>
      </div>
    );
  }

  // For now, just show the current folder name
  // In a real implementation, you'd maintain the full path structure
  return (
    <div className="flex items-center space-x-1 text-sm text-slate-600">
      <Home className="w-4 h-4" />
      <span>OneDrive</span>
      <ChevronRight className="w-3 h-3" />
      <span className="font-medium text-slate-900 dark:text-white">
        Current Folder
      </span>
    </div>
  );
}
