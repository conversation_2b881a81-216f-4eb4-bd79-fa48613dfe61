"use client";

import { ArrowLeft, Folder, File, Download, Loader2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useOneDrive } from "../../../hooks/data-integrations/useOneDrive";
import { formatFileSize } from "../../../utils/fileUtils";
import { formatDate } from "../../../utils/dateUtils";
import OneDriveBreadcrumbs from "./OneDriveBreadcrumbs";

export default function OneDriveFileList() {
  const {
    path,
    items,
    history,
    loading,
    navigateToFolder,
    goBack,
    addFileToRAG,
    fileStatuses,
  } = useOneDrive();

  const downloadFile = (item: any) => {
    // Open file in new tab
    window.open(item.webUrl, "_blank");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-sm text-slate-600">Loading files...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        <OneDriveBreadcrumbs path={path} />
        {history.length > 0 && (
          <Button variant="outline" size="sm" onClick={goBack}>
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back
          </Button>
        )}
      </div>

      {/* File List */}
      <ScrollArea className="h-64 border rounded-lg">
        <div className="p-2">
          {items.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Folder className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">This folder is empty</p>
            </div>
          ) : (
            <div className="space-y-1">
              {items.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-2 rounded hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer"
                  onClick={() => {
                    if (!!item.folder) {
                      navigateToFolder(item.id);
                    }
                  }}
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {!!item.folder ? (
                      <Folder className="w-4 h-4 text-blue-500 flex-shrink-0" />
                    ) : (
                      <File className="w-4 h-4 text-slate-500 flex-shrink-0" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                        {item.name}
                      </p>
                      {!item.folder && (
                        <div className="flex items-center space-x-2 text-xs text-slate-500">
                          <span>{formatFileSize(item.size)}</span>
                          <span>•</span>
                          <span>{formatDate(item.lastModifiedDateTime)}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {!item.folder && (
                    <div className="flex items-center space-x-1">
                      {fileStatuses[item.id] === "Processing" ? (
                        <Badge variant="outline">Processing</Badge>
                      ) : fileStatuses[item.id] === "Added" ||
                        item.ragStatus?.inRAG ? (
                        <Badge variant="secondary">Added</Badge>
                      ) : fileStatuses[item.id] === "Error" ? (
                        <Badge variant="destructive">Error</Badge>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            addFileToRAG(item.id, item.name);
                          }}
                          className="flex-shrink-0"
                          title="Add to Houston"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadFile(item);
                        }}
                        className="flex-shrink-0"
                        title="Open file"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
