"use client";

import { TabsContent } from "@/components/ui/tabs";
import OneDriveCard from "./onedrive/OneDriveCard";
import GoogleDriveCard from "./googledrive/GoogleDriveCard";
import SharePointCard from "./sharepoint/SharePointCard";

export default function DataIntegrationsTab() {
  return (
    <TabsContent value="cloud" className="mt-0 h-full">
      <div className="space-y-6 max-w-6xl mx-auto">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Data Integrations
          </h2>
          <p className="text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Connect your cloud storage services to access and manage your files
            directly from Talknician. Seamlessly integrate with your existing
            workflows.
          </p>
        </div>

        <div className="flex flex-col gap-6">
          <OneDriveCard />
          <GoogleDriveCard />
          <SharePointCard />
        </div>

        {/* Feature highlights */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">
            🚀 What you can do with integrations
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-slate-600 dark:text-slate-400">
                Browse and access files directly from your cloud storage
              </span>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-slate-600 dark:text-slate-400">
                Add documents to Houston for AI-powered insights
              </span>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-slate-600 dark:text-slate-400">
                Sync content automatically with your knowledge base
              </span>
            </div>
          </div>
        </div>
      </div>
    </TabsContent>
  );
}
