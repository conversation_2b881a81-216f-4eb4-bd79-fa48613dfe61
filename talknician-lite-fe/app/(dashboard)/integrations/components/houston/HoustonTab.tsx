"use client";

import { useState } from "react";
import {
  CheckCircle,
  AlertCircle,
  Loader2,
  FileText,
  Upload,
  Trash2,
} from "lucide-react";
import { TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useDocumentsQuery } from "@/api/hooks";
import { useFileOperations } from "../../hooks/files/useFileOperations";
import { formatFileSize } from "../../utils/fileUtils";
import { formatDate } from "../../utils/dateUtils";
import ConfirmationModal from "../shared/ConfirmationModal";

export default function HoustonTab() {
  const { currentOrganization } = useOrganization();
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<(() => void) | null>(null);
  const [confirmMessage, setConfirmMessage] = useState("");
  const [batchLoading, setBatchLoading] = useState(false);

  const { data: documentsResponse, isLoading: documentsLoading } =
    useDocumentsQuery(currentOrganization?.id || "");

  const {
    handleAddToRAG,
    handleRemoveFromRAG,
    addToRAGMutation,
    removeFromRAGMutation,
  } = useFileOperations();

  const documents = documentsResponse?.data || [];
  const ragDocuments = documents.filter((doc) => doc.inRAG);
  const eligibleDocuments = documents.filter(
    (doc) => !doc.inRAG && doc.status === "COMPLETED"
  );
  const processingDocuments = documents.filter(
    (doc) => doc.status === "PROCESSING"
  );

  const handleBatchAddToRAG = async () => {
    setBatchLoading(true);
    for (const doc of eligibleDocuments) {
      try {
        await handleAddToRAG(doc.id);
      } catch (error) {
        console.error(`Failed to add ${doc.originalName} to RAG:`, error);
      }
    }
    setBatchLoading(false);
  };

  const confirmBatchAdd = () => {
    setConfirmMessage(
      `Add all ${eligibleDocuments.length} eligible documents to Houston?`
    );
    setConfirmAction(() => handleBatchAddToRAG);
    setConfirmModalOpen(true);
  };

  if (documentsLoading) {
    return (
      <TabsContent value="rag" className="mt-0 h-full">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
            <span className="text-slate-500">Loading Houston data...</span>
          </div>
        </div>
      </TabsContent>
    );
  }

  return (
    <TabsContent value="rag" className="mt-0 h-full">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Houston Document Processing
          </h2>
          <p className="text-slate-600 dark:text-slate-400">
            Advanced document processing and AI-powered insights
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-slate-900 dark:text-white">
                In Houston
              </span>
            </div>
            <p className="text-2xl font-bold text-green-600 mt-1">
              {ragDocuments.length}
            </p>
          </div>

          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="flex items-center space-x-2">
              <Loader2 className="w-5 h-5 text-yellow-600" />
              <span className="text-sm font-medium text-slate-900 dark:text-white">
                Processing
              </span>
            </div>
            <p className="text-2xl font-bold text-yellow-600 mt-1">
              {processingDocuments.length}
            </p>
          </div>

          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="flex items-center space-x-2">
              <Upload className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-slate-900 dark:text-white">
                Available
              </span>
            </div>
            <p className="text-2xl font-bold text-blue-600 mt-1">
              {eligibleDocuments.length}
            </p>
          </div>
        </div>

        {/* Actions */}
        {eligibleDocuments.length > 0 && (
          <div>
            <Button
              onClick={confirmBatchAdd}
              disabled={batchLoading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              {batchLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              Add All to Houston ({eligibleDocuments.length})
            </Button>
          </div>
        )}

        {/* Documents in Houston */}
        {ragDocuments.length > 0 && (
          <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="p-4 border-b border-slate-200 dark:border-slate-700">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Documents in Houston ({ragDocuments.length})
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                These documents are available to Houston for AI-powered
                responses
              </p>
            </div>
            <ScrollArea className="max-h-96">
              <div className="p-4 space-y-3">
                {ragDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="w-4 h-4 text-slate-500" />
                      <div>
                        <p className="text-sm font-medium text-slate-900 dark:text-white">
                          {doc.originalName}
                        </p>
                        <p className="text-xs text-slate-500">
                          {formatFileSize(doc.size)} •{" "}
                          {formatDate(doc.createdAt)}
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRemoveFromRAG(doc.id)}
                      disabled={removeFromRAGMutation.isPending}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Remove
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Processing Documents */}
        {processingDocuments.length > 0 && (
          <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="p-4 border-b border-slate-200 dark:border-slate-700">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Processing Documents ({processingDocuments.length})
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                These documents are being processed and will be available soon
              </p>
            </div>
            <div className="p-4 space-y-3">
              {processingDocuments.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700"
                >
                  <div className="flex items-center space-x-3">
                    <Loader2 className="w-4 h-4 text-yellow-600 animate-spin" />
                    <div>
                      <p className="text-sm font-medium text-slate-900 dark:text-white">
                        {doc.originalName}
                      </p>
                      <p className="text-xs text-yellow-600">
                        Processing for Houston...
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant="outline"
                    className="border-yellow-200 text-yellow-700 bg-yellow-50"
                  >
                    Processing
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Available Documents */}
        {eligibleDocuments.length > 0 && (
          <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="p-4 border-b border-slate-200 dark:border-slate-700">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Available Documents ({eligibleDocuments.length})
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                These documents can be added to Houston
              </p>
            </div>
            <ScrollArea className="max-h-96 overflow-auto">
              <div className="p-4 space-y-3">
                {eligibleDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="w-4 h-4 text-slate-500" />
                      <div>
                        <p className="text-sm font-medium text-slate-900 dark:text-white">
                          {doc.originalName}
                        </p>
                        <p className="text-xs text-slate-500">
                          {formatFileSize(doc.size)} •{" "}
                          {formatDate(doc.createdAt)}
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleAddToRAG(doc.id)}
                      disabled={addToRAGMutation.isPending}
                      className="border-indigo-200 text-indigo-600"
                    >
                      <Upload className="w-3 h-3 mr-1" />
                      Add to Houston
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Empty State */}
        {documents.length === 0 && (
          <div className="text-center py-16">
            <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
              No documents yet
            </h3>
            <p className="text-slate-600 dark:text-slate-400">
              Upload documents in the Files tab to start using Houston
            </p>
          </div>
        )}

        {/* Confirmation Modal */}
        <ConfirmationModal
          open={confirmModalOpen}
          onOpenChange={setConfirmModalOpen}
          message={confirmMessage}
          onConfirm={async () => {
            if (confirmAction) {
              await confirmAction();
              setConfirmModalOpen(false);
              setConfirmAction(null);
            }
          }}
          loading={batchLoading}
        />
      </div>
    </TabsContent>
  );
}
