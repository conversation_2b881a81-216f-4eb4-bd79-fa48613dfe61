import { Document } from "@/api/documents";

export interface FileUploadState {
  uploadingFiles: File[];
  isUploading: boolean;
}

export interface FileOperationState {
  processingRAGIds: string[];
  batchLoading: boolean;
}

export interface RenameModalState {
  open: boolean;
  file: Document | null;
  value: string;
  error: string;
}

export interface DocumentModalState {
  open: boolean;
  url: string;
  title: string;
  filename: string;
}

export interface ConfirmationModalState {
  open: boolean;
  message: string;
  action: (() => void) | null;
}

export type ViewMode = "grid" | "list";
