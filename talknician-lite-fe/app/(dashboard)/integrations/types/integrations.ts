export interface CloudIntegration {
  id: string;
  name: string;
  type: "onedrive" | "googledrive" | "sharepoint";
  connected: boolean;
  filesCount?: number;
  lastSync?: Date;
}

export interface OneDriveConnectionState {
  connected: boolean | null;
  loading: boolean;
  connecting: boolean;
  error: string;
}

export interface OneDriveNavigationState {
  path: string;
  items: import("@/api").OneDriveItem[];
  history: string[];
}

// Re-export OneDriveItem from API to ensure consistency
export type { OneDriveItem } from "@/api";

export interface OneDriveCache {
  [path: string]: import("@/api").OneDriveItem[];
}
