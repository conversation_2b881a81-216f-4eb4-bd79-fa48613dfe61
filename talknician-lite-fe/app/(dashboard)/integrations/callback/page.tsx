"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function IntegrationCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    // Optionally show a message for a second, then navigate
    const timeout = setTimeout(() => {
      router.replace("/integrations?tab=cloud");
    }, 1000);
    return () => clearTimeout(timeout);
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <h2 className="text-xl font-bold mb-2">Integration Successful!</h2>
      <p className="text-slate-600">Redirecting...</p>
    </div>
  );
}
