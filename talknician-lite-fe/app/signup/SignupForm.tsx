"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2, AlertCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface InvitationData {
  id: string;
  email: string;
  role: string;
  organizationName: string;
  expiresAt: string;
  isValid: boolean;
}

export default function SignupForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const invitationId = searchParams.get("invitation");

  useEffect(() => {
    const validateInvitation = async () => {
      if (!invitationId) {
        // No invitation, redirect to regular register
        router.replace("/register");
        return;
      }

      try {
        // Validate invitation with backend
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/organizations/invitations/${invitationId}/validate`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Invitation not found or has expired");
          }
          throw new Error("Failed to validate invitation");
        }

        const data = await response.json();
        setInvitation(data.data);

        // Redirect to register with invitation data
        const params = new URLSearchParams({
          invitation: invitationId,
          email: data.data.email || "",
          organization: data.data.organizationName || "",
        });

        router.replace(`/register?${params.toString()}`);
      } catch (err) {
        console.error("Invitation validation error:", err);
        setError(err instanceof Error ? err.message : "Invalid invitation");
      } finally {
        setLoading(false);
      }
    };

    validateInvitation();
  }, [invitationId, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-indigo-600" />
            <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
              Validating Invitation
            </h2>
            <p className="text-slate-600 dark:text-slate-300">
              Please wait while we validate your invitation...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
              Invalid Invitation
            </h2>
            <p className="text-slate-600 dark:text-slate-300 mb-6">{error}</p>
            <div className="space-y-3">
              <Button
                asChild
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
              >
                <Link href="/register">Create Account</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="w-full border-slate-200 dark:border-slate-600"
              >
                <Link href="/login">Sign In</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // This shouldn't render as we redirect in useEffect, but just in case
  return null;
}
