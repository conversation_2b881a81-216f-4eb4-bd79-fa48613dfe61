import { Suspense } from "react";
import SignupForm from "./SignupForm";
import { Skeleton } from "@/components/ui/skeleton";

function SignupLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md border border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800 rounded-lg">
        <div className="p-8 text-center">
          <Skeleton className="w-8 h-8 rounded-full mx-auto mb-4" />
          <Skeleton className="h-6 w-48 mx-auto mb-2" />
          <Skeleton className="h-4 w-64 mx-auto" />
        </div>
      </div>
    </div>
  );
}

export default function SignupPage() {
  return (
    <Suspense fallback={<SignupLoading />}>
      <SignupForm />
    </Suspense>
  );
}
