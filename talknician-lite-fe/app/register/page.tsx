import { Suspense } from "react";
import RegisterForm from "./RegisterForm";
import { Skeleton } from "@/components/ui/skeleton";

function RegisterLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center space-x-3">
            <Skeleton className="w-12 h-12 rounded-xl" />
            <Skeleton className="h-7 w-32" />
          </div>
          <Skeleton className="h-5 w-48 mt-2 mx-auto" />
        </div>

        <div className="flex justify-center mb-6">
          <Skeleton className="w-20 h-10" />
        </div>

        <div className="border-slate-200 dark:border-slate-700 shadow-xl bg-white dark:bg-slate-800 rounded-lg p-8 space-y-6">
          <div className="text-center">
            <Skeleton className="h-7 w-40 mx-auto" />
            <Skeleton className="h-4 w-64 mx-auto mt-2" />
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="flex items-start space-x-2 pt-2">
              <Skeleton className="w-5 h-5 rounded-sm mt-1" />
              <div className="space-y-1 flex-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>
            <Skeleton className="h-12 w-full mt-4" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<RegisterLoading />}>
      <RegisterForm />
    </Suspense>
  );
}
