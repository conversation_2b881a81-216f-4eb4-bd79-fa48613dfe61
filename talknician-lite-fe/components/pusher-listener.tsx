"use client";

import { useEffect } from "react";
import { usePusher } from "@/hooks/usePusher";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useOneDriveStore } from "@/stores/useOneDriveStore";

export function PusherListener() {
  const pusher = usePusher();
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  const { navigateToFolder, path } = useOneDriveStore();

  useEffect(() => {
    if (!pusher || !currentOrganization) return;

    const channelName = `private-org-${currentOrganization.id}`;
    let channel = pusher.channel(channelName);
    if (!channel) {
      channel = pusher.subscribe(channelName);
    }

    const fileStatusUpdateHandler = (data: any) => {
      console.log("Pusher event received: file-status-update", data);
      // Refresh the current folder
      navigateToFolder(path, currentOrganization.id, accessToken);
    };

    const fileDeletedHandler = (data: any) => {
      console.log("Pusher event received: file-deleted-from-rag", data);
      // Refresh the current folder
      navigateToFolder(path, currentOrganization.id, accessToken);
    };

    channel.bind("file-status-update", fileStatusUpdateHandler);
    channel.bind("file-deleted-from-rag", fileDeletedHandler);

    return () => {
      if (channel) {
        channel.unbind("file-status-update", fileStatusUpdateHandler);
        channel.unbind("file-deleted-from-rag", fileDeletedHandler);
        // We don't unsubscribe here, as the main hook manages the connection lifecycle
      }
    };
  }, [pusher, currentOrganization, navigateToFolder, path, accessToken]);

  return null; // This is a listener component, it doesn't render anything
}
