"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PlusIcon, MessageSquare, Loader2 } from "lucide-react";
import { useChatStore } from "@/stores/useChatStore";

interface ChatSidebarProps {
  onCreateConversation: () => void;
  onSelectConversation: (conversationId: string) => void;
  isCreatingConversation?: boolean;
  conversationSearchQuery?: string;
}

export function ChatSidebar({
  onCreateConversation,
  onSelectConversation,
  isCreatingConversation = false,
  conversationSearchQuery = "",
}: ChatSidebarProps) {
  const { conversations, currentConversation } = useChatStore();

  // Filter conversations based on search query
  const filteredConversations = conversations.filter((conv) =>
    conv.title.toLowerCase().includes(conversationSearchQuery.toLowerCase())
  );

  return (
    <div className="w-64 border-r bg-slate-50 dark:bg-slate-900 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <Button
          onClick={onCreateConversation}
          disabled={isCreatingConversation}
          className="w-full"
        >
          {isCreatingConversation ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <PlusIcon className="w-4 h-4 mr-2" />
          )}
          New Chat
        </Button>
      </div>

      {/* Conversations List */}
      <ScrollArea className="flex-1">
        {filteredConversations.length === 0 ? (
          <div className="p-4">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              {conversationSearchQuery
                ? "No conversations match your search"
                : "No conversations yet. Create your first one!"}
            </p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredConversations.map((conversation) => (
              <Button
                key={conversation.id}
                variant={
                  currentConversation?.id === conversation.id
                    ? "secondary"
                    : "ghost"
                }
                onClick={() => onSelectConversation(conversation.id)}
                className="w-full justify-start text-left h-auto p-3"
              >
                <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{conversation.title}</span>
              </Button>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
