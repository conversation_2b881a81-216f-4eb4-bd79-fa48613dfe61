"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { FileText, Globe, ExternalLink, Sparkles } from "lucide-react";
import { MessageReference } from "@/types/chat";

interface SourceReferenceProps {
  reference: MessageReference;
}

export function SourceReference({ reference }: SourceReferenceProps) {
  const getSourceIcon = () => {
    switch (reference.type) {
      case "DOCUMENT":
        return <FileText className="h-3 w-3" />;
      case "WEBSITE":
        return <Globe className="h-3 w-3" />;
      default:
        return <Sparkles className="h-3 w-3" />;
    }
  };

  const handleSourceClick = () => {
    if (reference.url) {
      window.open(reference.url, "_blank");
    } else if (reference.azureBlobUrl) {
      window.open(reference.azureBlobUrl, "_blank");
    }
  };

  return (
    <Badge
      variant="outline"
      className="text-xs cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      onClick={handleSourceClick}
    >
      {getSourceIcon()}
      <span className="ml-1">{reference.title}</span>
      {(reference.url || reference.azureBlobUrl) && (
        <ExternalLink className="h-2 w-2 ml-1" />
      )}
    </Badge>
  );
}
