"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";

import { Plus, Edit, Trash2, User, Check } from "lucide-react";
import { ChatPersona } from "@/types/chat";
import { usePersonaApi } from "@/api/personas";
import { useOrganization } from "@/contexts/OrganizationContext";

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedPersonaId: string | null;
  onPersonaSelect: (personaId: string | null) => void;
  personas: ChatPersona[];
  refreshPersonas: () => void;
}

export function SettingsDialog({
  open,
  onOpenChange,
  selectedPersonaId,
  onPersonaSelect,
  personas,
  refreshPersonas,
}: SettingsDialogProps) {
  const apiClient = usePersonaApi();
  const { currentOrganization } = useOrganization();
  const [editingPersona, setEditingPersona] = useState<ChatPersona | null>(
    null
  );
  const [isCreating, setIsCreating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    systemPrompt: "",
    isPublic: false,
  });

  const handleCreateNew = () => {
    setFormData({ title: "", systemPrompt: "", isPublic: false });
    setEditingPersona(null);
    setIsCreating(true);
  };

  const handleEdit = (persona: ChatPersona) => {
    setFormData({
      title: persona.title,
      systemPrompt: persona.systemPrompt,
      isPublic: persona.isPublic,
    });
    setEditingPersona(persona);
    setIsCreating(true);
  };

  const handleSave = async () => {
    if (!formData.title.trim() || !formData.systemPrompt.trim()) return;

    try {
      setIsSaving(true);

      if (editingPersona) {
        // Update existing persona
        await apiClient.updatePersona(editingPersona.id, {
          title: formData.title.trim(),
          systemPrompt: formData.systemPrompt.trim(),
          isPublic: formData.isPublic,
        });
      } else {
        // Create new persona
        await apiClient.createPersona({
          title: formData.title.trim(),
          systemPrompt: formData.systemPrompt.trim(),
          isPublic: formData.isPublic,
          organizationId: currentOrganization?.id,
        });
      }

      refreshPersonas();
      setIsCreating(false);
      setEditingPersona(null);
    } catch (error) {
      console.error("Failed to save persona:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (personaId: string) => {
    try {
      setIsSaving(true);

      // If deleting the currently selected persona, clear selection
      if (selectedPersonaId === personaId) {
        onPersonaSelect(null);
      }

      await apiClient.deletePersona(personaId);
      refreshPersonas();
    } catch (error) {
      console.error("Failed to delete persona:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingPersona(null);
    setFormData({ title: "", systemPrompt: "", isPublic: false });
  };

  const handlePersonaSelect = (personaId: string) => {
    console.log("personaId", personaId);
    onPersonaSelect(personaId === "none" ? null : personaId);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Chat Personas</DialogTitle>
          <DialogDescription>
            Select a persona for the current conversation and manage your chat
            personas.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {!isCreating ? (
            <>
              {/* Persona Selection Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Select Active Persona</h3>
                <RadioGroup
                  value={selectedPersonaId || "none"}
                  onValueChange={handlePersonaSelect}
                  className="space-y-3"
                >
                  {/* No Persona Option */}
                  <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800">
                    <RadioGroupItem value="none" id="none" />
                    <Label htmlFor="none" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-slate-400" />
                        <span className="font-medium">No Persona</span>
                        <Badge variant="outline" className="text-xs">
                          Default
                        </Badge>
                      </div>
                      <p className="text-xs text-slate-500 mt-1">
                        Use the default AI assistant behavior
                      </p>
                    </Label>
                  </div>

                  {/* Available Personas */}
                  {personas.length === 0 ? (
                    <div className="text-center py-4 text-slate-500">
                      No personas found. Create one to get started!
                    </div>
                  ) : (
                    personas.map((persona) => (
                      <div
                        key={persona.id}
                        className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800"
                      >
                        <RadioGroupItem value={persona.id} id={persona.id} />
                        <Label
                          htmlFor={persona.id}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4" />
                            <span className="font-medium">{persona.title}</span>
                            {persona.isDefault && (
                              <Badge variant="secondary" className="text-xs">
                                Default
                              </Badge>
                            )}
                            {persona.isPublic && (
                              <Badge variant="outline" className="text-xs">
                                Public
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-slate-500 mt-1 line-clamp-2">
                            {persona.systemPrompt}
                          </p>
                        </Label>

                        {/* Action Buttons */}
                        <div className="flex gap-1">
                          {persona.isOwned && !persona.isDefault && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleEdit(persona);
                                }}
                                disabled={isSaving}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleDelete(persona.id);
                                }}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                disabled={isSaving}
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </RadioGroup>
              </div>

              <Separator />

              {/* Persona Management Section */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Manage Personas</h3>
                  <Button
                    onClick={handleCreateNew}
                    size="sm"
                    disabled={isSaving}
                    className="text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New
                  </Button>
                </div>
                <p className="text-xs text-slate-500">
                  Create custom personas to define how the AI assistant behaves
                  and responds.
                </p>
              </div>
            </>
          ) : (
            <>
              {/* Create/Edit Form */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">
                    {editingPersona ? "Edit Persona" : "Create New Persona"}
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Persona Name</Label>
                    <Input
                      id="title"
                      placeholder="e.g., Code Reviewer, Creative Writer, etc."
                      value={formData.title}
                      onChange={(e) =>
                        setFormData({ ...formData, title: e.target.value })
                      }
                      disabled={isSaving}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="systemPrompt">System Prompt</Label>
                    <Textarea
                      id="systemPrompt"
                      placeholder="Define how the AI should behave, its role, and any specific instructions..."
                      value={formData.systemPrompt}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          systemPrompt: e.target.value,
                        })
                      }
                      rows={6}
                      disabled={isSaving}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isPublic"
                      checked={formData.isPublic}
                      onChange={(e) =>
                        setFormData({ ...formData, isPublic: e.target.checked })
                      }
                      disabled={isSaving}
                      className="rounded"
                    />
                    <Label htmlFor="isPublic" className="text-sm">
                      Make this persona public (visible to others)
                    </Label>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={
                      !formData.title.trim() ||
                      !formData.systemPrompt.trim() ||
                      isSaving
                    }
                  >
                    {isSaving
                      ? "Saving..."
                      : editingPersona
                      ? "Update"
                      : "Create"}
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
