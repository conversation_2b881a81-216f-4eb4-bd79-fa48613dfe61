"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { User } from "lucide-react";
import { ChatPersona } from "@/types/chat";

interface PersonaPillProps {
  persona: ChatPersona;
  className?: string;
}

export function PersonaPill({ persona, className = "" }: PersonaPillProps) {
  return (
    <Badge
      variant="secondary"
      className={`text-xs flex items-center gap-1 ${className}`}
    >
      <User className="w-3 h-3" />
      {persona.title}
    </Badge>
  );
}
