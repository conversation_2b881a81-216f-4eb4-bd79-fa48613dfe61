"use client";

import React, { useEffect, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import { ChatMessage } from "@/components/houston/ChatMessage";
import { ChatMessage as ChatMessageType, StreamingState } from "@/types/chat";

interface MessagesListProps {
  messages: ChatMessageType[];
  streamingState: StreamingState;
  className?: string;
}

export const MessagesList = React.memo(function MessagesList({
  messages,
  streamingState,
  className,
}: MessagesListProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevMessagesLength = useRef(messages.length);
  const prevStreaming = useRef(streamingState.isStreaming);

  const isUserNearBottom = () => {
    const area = scrollAreaRef.current;
    if (!area) return false;
    const { scrollTop, scrollHeight, clientHeight } = area;
    return scrollHeight - (scrollTop + clientHeight) < 100;
  };

  // Only scroll on new message or streaming start/end
  useEffect(() => {
    const newMessage = messages.length > prevMessagesLength.current;
    const streamingChanged =
      streamingState.isStreaming !== prevStreaming.current;
    if ((newMessage || streamingChanged) && isUserNearBottom()) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
    prevMessagesLength.current = messages.length;
    prevStreaming.current = streamingState.isStreaming;
  }, [messages.length, streamingState.isStreaming]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <ScrollArea ref={scrollAreaRef} className={`flex-1 ${className}`}>
      <div className="p-4 space-y-4">
        {messages.length === 0 && !streamingState.isStreaming ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <div className="text-6xl mb-4">🚀</div>
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
              Welcome to Houston
            </h3>
            <p className="text-slate-600 dark:text-slate-400 max-w-md">
              Your AI assistant is ready to help. Ask questions about your
              documents, get insights, or start a conversation.
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}

            {/* Streaming message */}
            {streamingState.isStreaming && (
              <ChatMessage
                message={{
                  id: "streaming",
                  role: "assistant",
                  content: streamingState.currentContent,
                }}
                isStreaming={true}
                streamingContent={streamingState.currentContent}
                loadingMessage={streamingState.loadingMessage}
              />
            )}

            {/* Error state */}
            {streamingState.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{streamingState.error}</AlertDescription>
              </Alert>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
        {streamingState.isStreaming && <div style={{ height: 200 }} />}
      </div>
    </ScrollArea>
  );
});
