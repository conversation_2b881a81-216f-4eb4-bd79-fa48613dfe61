"use client";

import { useState } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { User, ExternalLink, FileText, Globe, Rocket } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ChatMessage as ChatMessageType, MessageReference } from "@/types/chat";
import DocumentModal from "./DocumentModal";
import Image from "next/image";
import { UploadedFileState } from "./ChatInput";

interface ChatMessageProps {
  message: ChatMessageType;
  isStreaming?: boolean;
  streamingContent?: string;
  loadingMessage?: string;
}

// Helper to get favicon for a website
const getFaviconUrl = (url: string | undefined) => {
  if (!url) return null;
  try {
    const domain = new URL(url).hostname;
    return `https://www.google.com/s2/favicons?domain=${domain}`;
  } catch {
    return null;
  }
};

// Helper to get file icon by extension
const getFileIcon = (filename: string) => {
  const ext = filename.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "pdf":
      return <FileText className="w-3 h-3 mr-1" />;
    default:
      return <FileText className="w-3 h-3 mr-1" />;
  }
};

// Helper to get enhanced file icon with viewable indicator
const getEnhancedFileIcon = (
  filename: string,
  hasAzureBlobUrl: boolean = false
) => {
  const ext = filename.split(".").pop()?.toLowerCase();

  // File types that can be viewed in the modal
  const viewableTypes = [
    "pdf",
    "txt",
    "csv",
    "docx",
    "doc",
    "xlsx",
    "xls",
    "pptx",
    "ppt",
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp",
    "svg",
    "html",
    "htm",
    "json",
    "xml",
    "md",
  ];

  const isViewable = viewableTypes.includes(ext || "");

  if (isViewable && hasAzureBlobUrl) {
    return (
      <div className="flex items-center mr-1">{getFileIcon(filename)}</div>
    );
  }

  return getFileIcon(filename);
};

// Helper to check if file can be viewed in modal
const canViewInModal = (filename: string) => {
  const ext = filename.split(".").pop()?.toLowerCase();
  const viewableTypes = [
    "pdf",
    "txt",
    "csv",
    "docx",
    "doc",
    "xlsx",
    "xls",
    "pptx",
    "ppt",
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp",
    "svg",
    "html",
    "htm",
    "json",
    "xml",
    "md",
  ];
  return viewableTypes.includes(ext || "");
};

export function ChatMessage({
  message,
  isStreaming,
  streamingContent,
  loadingMessage,
}: ChatMessageProps) {
  const isUser = message.role === "user";
  const displayContent = isStreaming ? streamingContent : message.content;

  // Document Modal state
  const [documentModalOpen, setDocumentModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<{
    url: string;
    title: string;
    filename?: string;
  } | null>(null);

  const handleReferenceClick = (ref: MessageReference) => {
    // Check if it's a document with azureBlobUrl that can be viewed
    console.log("ref", ref);
    console.log("ref.type", ref.type);
    console.log("ref.azureBlobUrl", ref.azureBlobUrl);
    console.log("ref.filename", ref.filename);
    if (
      ref.type === "DOCUMENT" &&
      ref.azureBlobUrl &&
      ref.filename &&
      canViewInModal(ref.filename)
    ) {
      console.log("ref", ref);
      setSelectedDocument({
        url: ref.azureBlobUrl,
        title: ref.title,
        filename: ref.filename,
      });
      setDocumentModalOpen(true);
    } else if (ref.url) {
      // For non-viewable files or websites, open in new tab
      window.open(ref.url, "_blank");
    }
  };

  const handleAttachmentClick = (doc: UploadedFileState) => {
    if (!doc) return;
    if (doc.azureBlobUrl && doc.filename && canViewInModal(doc.filename)) {
      setSelectedDocument({
        url: doc.azureBlobUrl,
        title: doc.filename,
        filename: doc.filename,
      });
      setDocumentModalOpen(true);
    } else if (doc.azureBlobUrl) {
      // Fallback for non-viewable files
      window.open(doc.azureBlobUrl, "_blank");
    }
  };

  return (
    <>
      <div className={`flex gap-3 ${isUser ? "justify-end" : "justify-start"}`}>
        {!isUser && (
          <Avatar className="w-8 h-8 mt-1">
            <AvatarFallback className="bg-indigo-100 text-indigo-600">
              <Rocket className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>
        )}

        <div className={`max-w-[80%] ${isUser ? "order-first" : ""}`}>
          <div
            className={`rounded-lg px-4 py-3 ${
              isUser
                ? "bg-indigo-600 text-white ml-auto"
                : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            }`}
          >
            {isUser ? (
              <div className="flex flex-col gap-2">
                <p className="whitespace-pre-wrap">{displayContent}</p>
                {message.messageDocuments &&
                  message.messageDocuments.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {message.messageDocuments.map((doc) => {
                        if (!doc) return null;

                        const isImage = doc.mimeType?.includes("image");
                        const isViewable =
                          doc.filename && canViewInModal(doc.filename);

                        if (isImage) {
                          return (
                            <div key={doc.id}>
                              <Image
                                src={doc.azureBlobUrl || ""}
                                alt={doc.filename || "Document"}
                                width={150}
                                height={150}
                                className="w-full h-auto rounded-md cursor-pointer"
                              />
                            </div>
                          );
                        }

                        // Render other files as badges/pills
                        return (
                          <Badge
                            key={doc.id}
                            variant="outline"
                            className={`text-xs transition-all duration-200 ${
                              isViewable
                                ? "cursor-pointer hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-700"
                                : ""
                            }`}
                            onClick={() =>
                              isViewable && handleAttachmentClick(doc)
                            }
                            title={
                              isViewable
                                ? "Click to view document"
                                : doc.filename
                            }
                          >
                            {getEnhancedFileIcon(
                              doc.filename || "",
                              !!doc.azureBlobUrl
                            )}
                            {doc.filename}
                          </Badge>
                        );
                      })}
                    </div>
                  )}
              </div>
            ) : (
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    p: ({ children }) => (
                      <p className="mb-2 last:mb-0">{children}</p>
                    ),
                    ul: ({ children }) => (
                      <ul className="mb-2 last:mb-0 pl-4">{children}</ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="mb-2 last:mb-0 pl-4">{children}</ol>
                    ),
                    li: ({ children }) => <li className="mb-1">{children}</li>,
                    table: ({ children }) => (
                      <div className="overflow-x-auto">
                        <table className="min-w-full my-2 border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
                          {children}
                        </table>
                      </div>
                    ),
                    thead: ({ children }) => (
                      <thead className="bg-gray-50 dark:bg-gray-900/50">
                        {children}
                      </thead>
                    ),
                    tbody: ({ children }) => (
                      <tbody className="bg-white dark:bg-gray-800/50 divide-y divide-gray-200 dark:divide-gray-700">
                        {children}
                      </tbody>
                    ),
                    tr: ({ children }) => (
                      <tr className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        {children}
                      </tr>
                    ),
                    th: ({ children }) => (
                      <th
                        scope="col"
                        className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                      >
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                        {children}
                      </td>
                    ),
                    a: ({ href, children }) => {
                      // Extract domain from URL for display
                      const getDomain = (url: string) => {
                        try {
                          return new URL(url).hostname.replace("www.", "");
                        } catch {
                          return url;
                        }
                      };

                      return (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-1 px-2 py-1 mx-1 text-xs font-medium text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200 no-underline"
                        >
                          <Globe className="w-3 h-3" />
                          <span>{children}</span>
                          {href && (
                            <span className="text-blue-500 dark:text-blue-400 font-normal">
                              {getDomain(href)}
                            </span>
                          )}
                          <ExternalLink className="w-3 h-3" />
                        </a>
                      );
                    },
                    code: ({ children, className }) => {
                      const isInline = !className;
                      return isInline ? (
                        <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm">
                          {children}
                        </code>
                      ) : (
                        <pre className="bg-gray-200 dark:bg-gray-700 p-3 rounded-md overflow-x-auto">
                          <code>{children}</code>
                        </pre>
                      );
                    },
                  }}
                >
                  {displayContent || ""}
                </ReactMarkdown>
              </div>
            )}

            {isStreaming && !isUser && (
              <div className="flex items-center gap-1 mt-2 text-xs text-gray-500 align-middle">
                <span className="animate-pulse text-white text-sm mr-[2px] pb-[2px]">
                  {loadingMessage}
                </span>
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                  <div
                    className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.1s" }}
                  />
                  <div
                    className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* References */}
          {message.references && message.references.length > 0 && (
            <div className="mt-2 space-y-1">
              <p className="text-xs text-gray-500 font-medium">Sources:</p>
              <div className="flex flex-wrap gap-2">
                {message.references.map((ref) => {
                  const isViewableDocument =
                    ref.type === "DOCUMENT" &&
                    ref.azureBlobUrl &&
                    ref.filename &&
                    canViewInModal(ref.filename);

                  return (
                    <Badge
                      key={ref.id}
                      variant="outline"
                      className={`text-xs cursor-pointer transition-all duration-200 ${
                        isViewableDocument
                          ? "hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-700"
                          : "hover:bg-gray-100 dark:hover:bg-gray-800"
                      }`}
                      onClick={() => handleReferenceClick(ref)}
                      title={
                        isViewableDocument
                          ? "Click to view document"
                          : "Click to open"
                      }
                    >
                      {ref.type === "DOCUMENT" ? (
                        <>
                          {getEnhancedFileIcon(
                            ref.filename || ref.title,
                            ref.azureBlobUrl !== undefined
                          )}
                          {ref.filename || ref.title}
                        </>
                      ) : (
                        <>
                          {getFaviconUrl(ref.url) && (
                            <img
                              src={getFaviconUrl(ref.url)!}
                              alt=""
                              className="w-3 h-3 mr-1 rounded-sm inline-block"
                              style={{ verticalAlign: "middle" }}
                            />
                          )}
                          {ref.filename || ref.title}
                        </>
                      )}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          {/* Timestamp */}
          {message.createdAt && (
            <p className="text-xs text-gray-500 mt-1">
              {new Date(message.createdAt).toLocaleTimeString()}
            </p>
          )}
        </div>

        {isUser && (
          <Avatar className="w-8 h-8 mt-1">
            <AvatarFallback className="bg-blue-100 text-blue-600">
              <User className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>
        )}
      </div>

      {/* Document Modal */}
      {selectedDocument && (
        <DocumentModal
          open={documentModalOpen}
          onOpenChange={setDocumentModalOpen}
          documentUrl={selectedDocument.url}
          title={selectedDocument.title}
          filename={selectedDocument.filename}
        />
      )}
    </>
  );
}
