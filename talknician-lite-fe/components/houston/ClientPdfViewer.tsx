import { useState } from "react";
import { Document, Page } from "react-pdf";
import { pdfjs } from "react-pdf";
import { Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";

import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface ClientPdfViewerProps {
  url: string;
}

function ClientPdfViewer({ url }: ClientPdfViewerProps) {
  const [numPages, setNumPages] = useState<number>();
  const [zoom, setZoom] = useState<number>(100); // percent

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
  }

  const handleZoomIn = () => setZoom((z) => Math.min(z + 10, 200));
  const handleZoomOut = () => setZoom((z) => Math.max(z - 10, 50));

  return (
    <div className="flex flex-col items-center justify-center w-full h-full p-4">
      {/* PDF Viewer Container */}
      <div className="relative w-full max-w-4xl h-[70vh] flex flex-col items-center justify-start bg-gray-50 dark:bg-gray-900 rounded shadow">
        {/* Zoom Controls - fixed bottom left, outside scrollable area, smaller */}
        <div className="absolute left-2 bottom-2 z-20 flex items-center gap-2 bg-white/80 dark:bg-gray-800/80 rounded px-1 py-0.5 shadow border border-gray-200 dark:border-gray-700 text-xs">
          <Button
            variant="outline"
            size="icon"
            className="h-6 w-6 p-0"
            onClick={handleZoomOut}
            disabled={zoom <= 50}
          >
            -
          </Button>
          <span className="min-w-[40px] text-center">{zoom}%</span>
          <Button
            variant="outline"
            size="icon"
            className="h-6 w-6 p-0"
            onClick={handleZoomIn}
            disabled={zoom >= 200}
          >
            +
          </Button>
        </div>
        {/* Scrollable PDF area */}
        <div className="w-full h-full overflow-auto flex flex-col items-center justify-start">
          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            loading={
              <div className="flex flex-col items-center justify-center w-full h-[60vh]">
                <Loader2 className="animate-spin w-8 h-8 text-gray-400 mb-2" />
                <span className="text-gray-500 text-sm">Loading PDF...</span>
              </div>
            }
          >
            {Array.from(new Array(numPages), (el, index) => (
              <Page
                key={`page_${index + 1}`}
                pageNumber={index + 1}
                width={800 * (zoom / 100)}
                className="mb-4 flex justify-center"
              />
            ))}
          </Document>
        </div>
      </div>
    </div>
  );
}

export default ClientPdfViewer;
