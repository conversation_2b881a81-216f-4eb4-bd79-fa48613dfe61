"use client";

import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  ExternalLink,
  FileText,
  FileSpreadsheet,
  FileImage,
  FileCode,
  File as FileIcon,
} from "lucide-react";
import dynamic from "next/dynamic";

const ClientPdfViewer = dynamic(() => import("./ClientPdfViewer"), {
  ssr: false,
});

interface DocumentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentUrl: string;
  title: string;
  filename?: string;
}

// File type detection and viewer capabilities
const getFileType = (filename: string) => {
  const ext = filename.split(".").pop()?.toLowerCase();
  return ext || "";
};

const getFileIcon = (filename: string) => {
  const ext = getFileType(filename);
  switch (ext) {
    case "pdf":
      return <FileText className="w-5 h-5 text-red-500" />;
    case "xlsx":
    case "xls":
    case "csv":
      return <FileSpreadsheet className="w-5 h-5 text-green-600" />;
    case "docx":
    case "doc":
      return <FileText className="w-5 h-5 text-blue-500" />;
    case "txt":
    case "md":
      return <FileCode className="w-5 h-5 text-gray-600" />;
    case "png":
    case "jpg":
    case "jpeg":
    case "gif":
    case "webp":
      return <FileImage className="w-5 h-5 text-purple-500" />;
    case "pptx":
    case "ppt":
      return <FileText className="w-5 h-5 text-orange-500" />;
    default:
      return <FileIcon className="w-5 h-5 text-gray-500" />;
  }
};

const canViewInBrowser = (filename: string) => {
  const ext = getFileType(filename);
  const viewableTypes = [
    "pdf", // PDF files
    "txt", // Plain text
    "csv", // CSV files
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp",
    "svg", // Images
    "html",
    "htm", // HTML files
    "json", // JSON files
    "xml", // XML files
    "md", // Markdown files
  ];
  return viewableTypes.includes(ext);
};

const canViewWithGoogleDocs = (filename: string) => {
  const ext = getFileType(filename);
  const googleDocsTypes = [
    "docx",
    "doc", // Word documents
    "xlsx",
    "xls", // Excel spreadsheets
    "pptx",
    "ppt", // PowerPoint presentations
  ];
  return googleDocsTypes.includes(ext);
};

const getViewerUrl = (documentUrl: string, filename: string) => {
  if (canViewInBrowser(filename)) {
    return documentUrl;
  }

  if (canViewWithGoogleDocs(filename)) {
    // Use Google Docs Viewer for Office documents
    return `https://docs.google.com/gview?url=${encodeURIComponent(
      documentUrl
    )}&embedded=true`;
  }

  return null;
};

const getFileTypeDescription = (filename: string) => {
  const ext = getFileType(filename);
  const descriptions: Record<string, string> = {
    pdf: "PDF Document",
    docx: "Word Document",
    doc: "Word Document",
    xlsx: "Excel Spreadsheet",
    xls: "Excel Spreadsheet",
    pptx: "PowerPoint Presentation",
    ppt: "PowerPoint Presentation",
    txt: "Text File",
    csv: "CSV Spreadsheet",
    md: "Markdown Document",
    png: "PNG Image",
    jpg: "JPEG Image",
    jpeg: "JPEG Image",
    gif: "GIF Image",
    webp: "WebP Image",
    svg: "SVG Image",
    json: "JSON Data",
    xml: "XML Document",
    html: "HTML Document",
    htm: "HTML Document",
  };

  return descriptions[ext] || `${ext.toUpperCase()} File`;
};

export default function DocumentModal({
  open,
  onOpenChange,
  documentUrl,
  title,
  filename,
}: DocumentModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [pdfData, setPdfData] = useState<ArrayBuffer | null>(null);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);

  const actualFilename = filename || title;
  const fileType = getFileType(actualFilename);
  const fileTypeDescription = getFileTypeDescription(actualFilename);
  const viewerUrl = getViewerUrl(documentUrl, actualFilename);
  const canView = viewerUrl !== null;
  const isPdf = fileType === "pdf";

  // Reset loading state when modal opens
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      setHasError(false);
      setPdfData(null);
      setNumPages(null);
      setPageNumber(1);
      // If PDF, fetch as ArrayBuffer
      if (isPdf) {
        fetch(documentUrl)
          .then((res) => res.arrayBuffer())
          .then((data) => {
            setPdfData(data);
            setIsLoading(false);
          })
          .catch(() => {
            setHasError(true);
            setIsLoading(false);
          });
      }
    }
  }, [open, documentUrl, isPdf]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return;

      if (event.key === "Escape") {
        onOpenChange(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [open, onOpenChange]);

  const handleOpenInNewTab = () => {
    window.open(documentUrl, "_blank", "noopener,noreferrer");
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const renderViewer = () => {
    if (isPdf) {
      return <ClientPdfViewer url={documentUrl} />;
    }
    return <div>Viewer not available</div>;
  };

  // Always return JSX
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl w-[95vw] h-[90vh] max-h-[90vh] p-0 gap-0 flex flex-col">
        <DialogHeader className="px-6 py-10 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getFileIcon(actualFilename)}
              <div className="flex flex-col">
                <DialogTitle className="text-lg font-semibold truncate">
                  {title}
                </DialogTitle>
                <span className="text-xs text-gray-500">
                  {fileTypeDescription}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenInNewTab}
                className="flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Open in New Tab
              </Button>
            </div>
          </div>
        </DialogHeader>
        <div className="flex-1 w-full h-0 min-h-0 overflow-auto flex items-center justify-center bg-background">
          {renderViewer()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
