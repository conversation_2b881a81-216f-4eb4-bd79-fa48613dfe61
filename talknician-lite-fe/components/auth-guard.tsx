"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export function AuthGuard({ children, redirectTo = "/login" }: AuthGuardProps) {
  const { isAuthenticated, isLoading, error } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't redirect if still loading or if we're already on a public page
    if (isLoading) return;

    // If there's an auth error (like invalid token), redirect to login
    if (error || !isAuthenticated) {
      console.log("AuthGuard: Redirecting to login", {
        isAuthenticated,
        error,
        currentPath: pathname,
      });

      // Store the intended destination for redirect after login
      if (pathname && pathname !== "/") {
        sessionStorage.setItem("auth_redirect_after_login", pathname);
      }

      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, error, router, redirectTo, pathname]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-300">
            Checking authentication...
          </p>
        </div>
      </div>
    );
  }

  // If not authenticated or there's an error, don't render children
  if (!isAuthenticated || error) {
    return null;
  }

  // User is authenticated, render children
  return <>{children}</>;
}
