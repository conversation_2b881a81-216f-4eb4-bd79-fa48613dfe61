"use client";

import { useOrganizationRefresh } from '@/hooks/useOrganizationRefresh';

/**
 * Component that listens for organization changes and refreshes data accordingly
 * This should be placed in the dashboard layout to ensure all pages benefit from automatic refresh
 */
export function OrganizationRefreshListener() {
  // Use the hook to automatically refresh data when organization changes
  useOrganizationRefresh({
    refreshDocuments: true,
    refreshConversations: true,
    refreshPersonas: true,
    refreshSettings: true,
  });

  // This component doesn't render anything
  return null;
}
