import {
  BaseApiClient,
  createBaseApiClient,
  useBaseApiClient,
  ApiResponse,
} from "./base";
import { useAuth } from "@/contexts/AuthContext";
import { Document } from "./documents";
import { useMemo } from "react";

// OneDrive Item type (matching documents.ts format)
export interface OneDriveItem {
  id: string;
  name: string;
  folder?: object;
  file?: object;
  size: number;
  lastModifiedDateTime: string;
  webUrl: string;
  ragStatus?: {
    status: string;
    inRAG: boolean;
  } | null;
}

export interface OneDriveAddToRAGRequest {
  fileId: string;
  fileName: string;
  organizationId: string;
}

// OneDrive API Client
export class OneDriveApiClient extends BaseApiClient {
  // Get auth URL (for popup authentication)
  getOneDriveAuthUrl(): string {
    return `${this.baseUrl}/api/integrations/onedrive/auth`;
  }

  async listOneDriveFiles(path: string = "/"): Promise<OneDriveItem[]> {
    const response = await fetch(
      `${this.baseUrl}/api/integrations/onedrive/list?path=${encodeURIComponent(
        path
      )}`,
      {
        credentials: "include",
        headers: this.getAuthHeaders(),
      }
    );
    if (response.status === 401) console.log("Not connected to OneDrive");
    if (!response.ok) console.log("Failed to list OneDrive files");
    const data = await response.json();
    return data.data;
  }

  async isOneDriveConnected(): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/integrations/onedrive/status`,
        {
          credentials: "include",
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.ok) return false;
      const data = await response.json();
      return data.connected;
    } catch (e: any) {
      return false;
    }
  }

  async addOneDriveFileToRAG({
    fileId,
    fileName,
    organizationId,
  }: OneDriveAddToRAGRequest): Promise<ApiResponse<Document>> {
    return this.request("/api/integrations/onedrive/add-to-rag", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
      body: JSON.stringify({ fileId, fileName, organizationId }),
    });
  }

  async disconnectOneDrive(): Promise<ApiResponse<void>> {
    const response = await fetch(
      `${this.baseUrl}/api/integrations/onedrive/disconnect`,
      {
        method: "POST",
        credentials: "include",
        headers: this.getAuthHeaders(),
      }
    );
    if (!response.ok) {
      throw new Error("Failed to disconnect OneDrive");
    }
    return response.json();
  }
}

// Factory functions
export const createOneDriveApiClient = (
  accessToken: string | null
): OneDriveApiClient => {
  return new OneDriveApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useOneDriveApi = (): OneDriveApiClient => {
  const { accessToken } = useAuth();
  return useMemo(() => createOneDriveApiClient(accessToken), [accessToken]);
};
