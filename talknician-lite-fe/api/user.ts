import { BaseApiClient, createApiClientWithStoredToken } from "./base";

export interface UserProfile {
  id: string;
  email: string;
  name: string | null;
  avatar: string | null;
  auth0Id?: string;
  firstName: string;
  lastName: string;
  memberSince: string;
  lastUpdated: string;
  organizations?: Array<{
    id: string;
    name: string;
    slug: string;
    role: string;
  }>;
}

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  name?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

class UserApiClient extends BaseApiClient {
  // Get current user profile
  async getProfile(): Promise<UserProfile> {
    const response = await this.request<{ data: { profile: UserProfile } }>(
      "/api/users/profile"
    );
    return response.data.profile;
  }

  // Update user profile
  async updateProfile(data: UpdateProfileData): Promise<UserProfile> {
    const response = await this.request<{ data: { profile: UserProfile } }>(
      "/api/users/profile",
      {
        method: "PUT",
        body: JSON.stringify(data),
      }
    );
    return response.data.profile;
  }

  // Change password
  async changePassword(data: ChangePasswordData): Promise<void> {
    await this.request<void>("/api/users/change-password", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Upload avatar (placeholder)
  async uploadAvatar(file: File): Promise<string> {
    // This is a placeholder - in a real implementation you would handle file upload
    const formData = new FormData();
    formData.append("avatar", file);

    const response = await this.request<{ data: { avatarUrl: string } }>(
      "/api/users/upload-avatar",
      {
        method: "POST",
        body: formData,
      }
    );

    return response.data.avatarUrl;
  }
}

// Export a function that creates an instance of UserApiClient
export const createUserApiClient = () => {
  const baseClient = createApiClientWithStoredToken();
  return new UserApiClient(baseClient["baseUrl"], baseClient["getAuthHeaders"]);
};

// Export the API methods as a simple object for convenience
export const userApi = {
  getProfile: () => createUserApiClient().getProfile(),
  updateProfile: (data: UpdateProfileData) =>
    createUserApiClient().updateProfile(data),
  changePassword: (data: ChangePasswordData) =>
    createUserApiClient().changePassword(data),
  uploadAvatar: (file: File) => createUserApiClient().uploadAvatar(file),
};
