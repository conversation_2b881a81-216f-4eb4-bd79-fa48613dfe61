import { useAuth } from "@/contexts/AuthContext";
import { BaseApiClient, ApiResponse } from "./base";

export interface MessageDocument {
  id: string;
  filename: string;
  size: number;
  mimeType: string;
  azureBlobUrl: string | null;
  uploadedAt: string;
  messageId: string | null;
  userId: string | null;
  openaiFileId: string | null;
}

export class MessageDocumentApiClient extends BaseApiClient {
  async uploadMessageDocuments(
    files: File[],
    organizationId: string
  ): Promise<ApiResponse<MessageDocument[]>> {
    const formData = new FormData();
    formData.append("organizationId", organizationId);
    files.forEach((file) => {
      formData.append("files", file);
    });

    return this.request("/api/message-documents/upload", {
      method: "POST",
      body: formData,
    });
  }

  async deleteMessageDocument(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/message-documents/${id}`, {
      method: "DELETE",
    });
  }
}

export const useMessageDocumentApi = (): MessageDocumentApiClient => {
  const { accessToken } = useAuth();
  return new MessageDocumentApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};
