import { BaseApiClient, ApiResponse } from "./base";
import { ChatSettings, ChatSettingsResponse } from "@/types/chat";
import { useAuth } from "@/contexts/AuthContext";

// Chat API Client (for settings and general chat operations)
export class ChatApiClient extends BaseApiClient {
  async getChatSettings(organizationId: string): Promise<ChatSettings> {
    const response = await this.request<ChatSettingsResponse>(
      `/api/chat/settings?organizationId=${organizationId}`
    );
    return response.data;
  }

  async updateChatSettings(
    organizationId: string,
    settings: Partial<ChatSettings>
  ): Promise<ChatSettings> {
    const response = await this.request<ChatSettingsResponse>(
      "/api/chat/settings",
      {
        method: "PUT",
        body: JSON.stringify({ organizationId, ...settings }),
      }
    );
    return response.data;
  }

  async sendMessage(data: {
    conversationId: string;
    message: string;
    settings?: any;
  }): Promise<Response> {
    // Return raw response for streaming
    const url = `${this.baseUrl}/api/chat/send`;
    return fetch(url, {
      method: "POST",
      headers: {
        ...this.getAuthHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
  }
}

// Factory functions
export const createChatApiClient = (
  accessToken: string | null
): ChatApiClient => {
  return new ChatApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useChatApi = (): ChatApiClient => {
  const { accessToken } = useAuth();
  return createChatApiClient(accessToken);
};
