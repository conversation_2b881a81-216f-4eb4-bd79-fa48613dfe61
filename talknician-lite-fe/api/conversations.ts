import { BaseApiClient, ApiResponse } from "./base";
import { useAuth } from "@/contexts/AuthContext";
import {
  Conversation,
  ChatMessage,
  ConversationsResponse,
  ConversationResponse,
  CreateConversationResponse,
} from "@/types/chat";

// Conversation API Client
export class ConversationApiClient extends BaseApiClient {
  async getConversations(organizationId: string): Promise<Conversation[]> {
    const response = await this.request<ConversationsResponse>(
      `/api/chat/conversations?organizationId=${organizationId}`
    );
    return response.data.conversations;
  }

  async getConversation(
    conversationId: string
  ): Promise<{ conversation: Conversation; messages: ChatMessage[] }> {
    const response = await this.request<ConversationResponse>(
      `/api/chat/conversations/${conversationId}`
    );
    return {
      conversation: response.data.conversation,
      messages: response.data.messages,
    };
  }

  async createConversation(
    title: string = "New Conversation",
    organizationId?: string
  ): Promise<Conversation> {
    const response = await this.request<CreateConversationResponse>(
      "/api/chat/conversations",
      {
        method: "POST",
        body: JSON.stringify({
          title,
          organizationId,
        }),
      }
    );
    return response.data;
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await this.request(`/api/chat/conversations/${conversationId}`, {
      method: "DELETE",
    });
  }

  async updateConversationTitle(
    conversationId: string,
    title: string
  ): Promise<Conversation> {
    const response = await this.request<
      ApiResponse<{ conversation: Conversation }>
    >(`/api/chat/conversations/${conversationId}`, {
      method: "PUT",
      body: JSON.stringify({ title }),
    });
    return response.data.conversation;
  }
}

// Factory functions
export const createConversationApiClient = (
  accessToken: string | null
): ConversationApiClient => {
  return new ConversationApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useConversationApi = (): ConversationApiClient => {
  const { accessToken } = useAuth();
  return createConversationApiClient(accessToken);
};
