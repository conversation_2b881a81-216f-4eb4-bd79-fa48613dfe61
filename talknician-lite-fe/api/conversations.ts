import { BaseApiClient, ApiResponse } from "./base";
import { useAuth } from "@/contexts/AuthContext";
import {
  Conversation,
  ChatMessage,
  ConversationsResponse,
  ConversationResponse,
  CreateConversationResponse,
  ConversationFilter,
  ConversationVisibility,
  ConversationVisibilityUpdateRequest,
  ConversationVisibilityUpdateResponse,
} from "@/types/chat";

// Conversation API Client
export class ConversationApiClient extends BaseApiClient {
  async getConversations(
    organizationId: string,
    filter?: ConversationFilter
  ): Promise<Conversation[]> {
    const params = new URLSearchParams({ organizationId });
    if (filter) {
      params.append("filter", filter);
    }

    const response = await this.request<ConversationsResponse>(
      `/api/chat/conversations?${params.toString()}`
    );
    return response.data.conversations;
  }

  async getConversation(
    conversationId: string,
    organizationId: string
  ): Promise<{ conversation: Conversation; messages: ChatMessage[] }> {
    const response = await this.request<ConversationResponse>(
      `/api/chat/conversations/${conversationId}?organizationId=${organizationId}`
    );
    return {
      conversation: response.data.conversation,
      messages: response.data.messages,
    };
  }

  async createConversation(
    title: string = "New Conversation",
    organizationId?: string,
    visibility: ConversationVisibility = "private"
  ): Promise<Conversation> {
    const response = await this.request<CreateConversationResponse>(
      "/api/chat/conversations",
      {
        method: "POST",
        body: JSON.stringify({
          title,
          organizationId,
          visibility,
        }),
      }
    );
    return response.data;
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await this.request(`/api/chat/conversations/${conversationId}`, {
      method: "DELETE",
    });
  }

  async updateConversationTitle(
    conversationId: string,
    title: string
  ): Promise<Conversation> {
    const response = await this.request<
      ApiResponse<{ conversation: Conversation }>
    >(`/api/chat/conversations/${conversationId}`, {
      method: "PUT",
      body: JSON.stringify({ title }),
    });
    return response.data.conversation;
  }

  async updateConversationVisibility(
    conversationId: string,
    visibility: ConversationVisibility
  ): Promise<Conversation> {
    const response = await this.request<ConversationVisibilityUpdateResponse>(
      `/api/chat/conversations/${conversationId}/visibility`,
      {
        method: "PUT",
        body: JSON.stringify({ visibility }),
      }
    );
    return response.data.conversation;
  }
}

// Factory functions
export const createConversationApiClient = (
  accessToken: string | null
): ConversationApiClient => {
  return new ConversationApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useConversationApi = (): ConversationApiClient => {
  const { accessToken } = useAuth();
  return createConversationApiClient(accessToken);
};
