// Main API exports - centralized access to all API clients
export {
  BaseApiClient,
  createBaseApi<PERSON>lient,
  useBaseApiClient,
  createApiClientWithStoredToken,
} from "./base";

// Persona API
export {
  PersonaApiClient,
  createPersonaApiClient,
  usePersonaApi,
} from "./personas";

// Conversation API
export {
  ConversationApiClient,
  createConversationApiClient,
  useConversationApi,
} from "./conversations";

// Chat API
export { ChatApiClient, createChatApiClient, useChatApi } from "./chat";

// Document API
export {
  DocumentApiClient,
  createDocumentApiClient,
  useDocument<PERSON>pi,
  type Document,
  type DocumentsResponse,
  type DocumentResponse,
} from "./documents";

// Organization API
export {
  OrganizationApiClient,
  createOrganizationApiClient,
  useOrganizationApi,
  type Organization,
  type OrganizationsResponse,
  type OrganizationResponse,
} from "./organizations";

// OneDrive API
export {
  OneDriveApiClient,
  createOneDriveApiClient,
  useOne<PERSON><PERSON><PERSON><PERSON>,
  type OneDriveItem,
  type OneDriveAddToRAGRequest,
} from "./onedrive";

// React Query Hooks
export {
  useDocumentsQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useAddDocumentToRAGMutation,
  useRemoveDocumentFromRAGMutation,
  useRenameDocumentMutation,
  queryKeys,
} from "./hooks";
