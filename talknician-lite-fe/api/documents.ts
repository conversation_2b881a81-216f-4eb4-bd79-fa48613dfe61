import { BaseApiClient, ApiResponse } from "./base";
import { useAuth } from "@/contexts/AuthContext";

// Document types (matching the old api-client.ts structure)
export interface Document {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  status: "PROCESSING" | "COMPLETED" | "FAILED";
  organizationId: string;
  createdAt: string;
  openaiFileId?: string;
  inRAG?: boolean;
  azureBlobUrl?: string;
  author?: string;
}

export interface DocumentsResponse {
  documents: Document[];
  total?: number;
  page?: number;
  limit?: number;
}

export interface DocumentResponse {
  document: Document;
}

// Document API Client
export class DocumentApiClient extends BaseApiClient {
  async getDocuments(organizationId: string): Promise<ApiResponse<Document[]>> {
    return this.request(`/api/documents?organizationId=${organizationId}`);
  }

  async getDocumentById(documentId: string): Promise<Document | null> {
    try {
      const response = await this.request<ApiResponse<DocumentResponse>>(
        `/api/documents/${documentId}`
      );
      return response.data.document;
    } catch (error) {
      console.error("Error loading document:", error);
      return null;
    }
  }

  async uploadDocument(
    file: File,
    organizationId: string
  ): Promise<ApiResponse<Document>> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("organizationId", organizationId);

    return this.request("/api/documents/upload", {
      method: "POST",
      body: formData,
    });
  }

  async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/documents/${id}`, {
      method: "DELETE",
    });
  }

  async addDocumentToRAG(id: string): Promise<ApiResponse<Document>> {
    return this.request(`/api/documents/${id}/add-to-rag`, {
      method: "POST",
    });
  }

  async removeDocumentFromRAG(id: string): Promise<ApiResponse<Document>> {
    return this.request(`/api/documents/${id}/remove-from-rag`, {
      method: "POST",
    });
  }

  async downloadDocument(
    id: string
  ): Promise<{ blob: Blob; filename: string }> {
    const url = `${this.baseUrl}/api/documents/${id}/download`;
    const response = await fetch(url, {
      method: "GET",
      headers: this.getAuthHeaders(),
    });
    if (!response.ok) {
      throw new Error("Failed to download document");
    }
    // Try to get filename from Content-Disposition
    let filename = "document";
    const disposition = response.headers.get("Content-Disposition");
    if (disposition) {
      const match = disposition.match(/filename="(.+)"/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    const blob = await response.blob();
    return { blob, filename };
  }

  async renameDocument(
    id: string,
    originalName: string
  ): Promise<ApiResponse<{ id: string; originalName: string }>> {
    return this.request(`/api/documents/${id}`, {
      method: "PATCH",
      body: JSON.stringify({ originalName }),
    });
  }
}

// Factory functions
export const createDocumentApiClient = (
  accessToken: string | null
): DocumentApiClient => {
  return new DocumentApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useDocumentApi = (): DocumentApiClient => {
  const { accessToken } = useAuth();
  return createDocumentApiClient(accessToken);
};
