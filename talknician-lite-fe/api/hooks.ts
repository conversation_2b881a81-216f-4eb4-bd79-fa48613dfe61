import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useDocumentApi, Document } from "./documents";

// Query Keys
export const queryKeys = {
  conversations: (orgId: string) => ["conversations", orgId] as const,
  conversation: (id: string) => ["conversation", id] as const,
  documents: (orgId: string) => ["documents", orgId] as const,
  organizations: () => ["organizations"] as const,
  onedrive: {
    status: () => ["onedrive", "status"] as const,
    items: (path: string) => ["onedrive", "items", path] as const,
  },
};

// Document Hooks
export const useDocumentsQuery = (organizationId: string) => {
  const documentApi = useDocumentApi();

  return useQuery({
    queryKey: queryKeys.documents(organizationId),
    queryFn: () => documentApi.getDocuments(organizationId),
    enabled: !!organizationId,
    staleTime: 30 * 1000, // 30 seconds - shorter for better responsiveness
    refetchInterval: (query) => {
      // Poll every 3 seconds if there are any processing documents
      const apiResponse = query.state.data;
      if (!apiResponse?.data) return false;

      const hasProcessingDocs = apiResponse.data.some(
        (doc: Document) => doc.status === "PROCESSING"
      );
      return hasProcessingDocs ? 3000 : false;
    },
    refetchIntervalInBackground: true,
  });
};

export const useUploadDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      file,
      organizationId,
    }: {
      file: File;
      organizationId: string;
    }) => documentApi.uploadDocument(file, organizationId),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};

export const useDeleteDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.deleteDocument(id),
    onSuccess: () => {
      // Invalidate documents lists
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useAddDocumentToRAGMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.addDocumentToRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRemoveDocumentFromRAGMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => documentApi.removeDocumentFromRAG(id),
    onSuccess: () => {
      // Invalidate documents lists to refresh the inRAG status
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useRenameDocumentMutation = () => {
  const documentApi = useDocumentApi();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      originalName,
      organizationId,
    }: {
      id: string;
      originalName: string;
      organizationId: string;
    }) => documentApi.renameDocument(id, originalName),
    onSuccess: (response, variables) => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: queryKeys.documents(variables.organizationId),
      });
    },
  });
};
