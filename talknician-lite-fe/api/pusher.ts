import { BaseApiClient } from "./base";
import { useAuth } from "@/contexts/AuthContext";
import { useMemo } from "react";

export class PusherApiClient extends BaseApiClient {
  async authenticate(
    socketId: string,
    channelName: string,
    organizationId: string
  ): Promise<any> {
    const url = `${this.baseUrl}/api/pusher/auth`;
    const headers = {
      ...this.getAuthHeaders(),
      "Content-Type": "application/json",
    };
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify({
        socket_id: socketId,
        channel_name: channelName,
        organizationId,
      }),
    });
    if (!response.ok) {
      throw new Error("Failed to authenticate with <PERSON><PERSON><PERSON>");
    }
    return response.json();
  }
}

export const usePusherApi = (): PusherApiClient => {
  const { accessToken } = useAuth();
  return useMemo(
    () =>
      new PusherApiClient(
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
        () => ({
          "Content-Type": "application/json",
          ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        })
      ),
    [accessToken]
  );
};
