import { create } from "zustand";
import { StreamingState } from "@/types/chat";

interface StreamingStoreState {
  streamingState: StreamingState;
  setStreamingState: (updates: Partial<StreamingState>) => void;
  resetStreaming: () => void;
}

const initialStreamingState: StreamingState = {
  isStreaming: false,
  currentContent: "",
  loadingMessage: "",
  error: null,
  status: "idle",
};

export const useStreamingStore = create<StreamingStoreState>((set) => ({
  streamingState: initialStreamingState,

  setStreamingState: (updates) =>
    set((state) => ({
      streamingState: { ...state.streamingState, ...updates },
    })),

  resetStreaming: () => set({ streamingState: initialStreamingState }),
}));
