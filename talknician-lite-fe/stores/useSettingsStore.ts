import { create } from "zustand";
import { ChatPersona } from "@/types/chat";

interface SettingsState {
  // Persona settings
  selectedPersonaId: string | null;
  currentPersona: ChatPersona | null;

  // Search settings
  currentRequestWebSearch: boolean;
  currentRequestFileSearch: boolean;

  // Actions
  setSelectedPersonaId: (id: string | null) => void;
  setCurrentPersona: (persona: ChatPersona | null) => void;
  setCurrentRequestWebSearch: (enabled: boolean) => void;
  setCurrentRequestFileSearch: (enabled: boolean) => void;
}

export const useSettingsStore = create<SettingsState>((set) => ({
  // Initial state
  selectedPersonaId: null,
  currentPersona: null,
  currentRequestWebSearch: false,
  currentRequestFileSearch: false,

  // Actions
  setSelectedPersonaId: (id) => set({ selectedPersonaId: id }),
  setCurrentPersona: (persona) => set({ currentPersona: persona }),
  setCurrentRequestWebSearch: (enabled) =>
    set({ currentRequestWebSearch: enabled }),
  setCurrentRequestFileSearch: (enabled) =>
    set({ currentRequestFileSearch: enabled }),
}));
