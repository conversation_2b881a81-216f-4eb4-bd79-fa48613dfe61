import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { ChatMessage, Conversation, ChatSettings } from "@/types/chat";

interface ChatState {
  // Current conversation and messages
  currentConversation: Conversation | null;
  messages: ChatMessage[];

  // Conversations list
  conversations: Conversation[];

  // Chat settings (from server)
  chatSettings: ChatSettings;

  // Actions
  setCurrentConversation: (conversation: Conversation | null) => void;
  setMessages: (messages: ChatMessage[]) => void;
  addMessage: (message: ChatMessage) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  clearMessages: () => void;

  setConversations: (conversations: Conversation[]) => void;
  addConversation: (conversation: Conversation) => void;
  removeConversation: (conversationId: string) => void;

  setChatSettings: (settings: ChatSettings) => void;

  // Reset store (when changing conversations)
  resetMessages: () => void;
  resetStore: () => void;
}

const initialChatSettings: ChatSettings = {
  documentSearch: true,
  enableWebSearch: false,
  personality: "helpful",
  systemPrompt: "",
};

export const useChatStore = create<ChatState>()(
  devtools(
    (set) => ({
      // Initial state
      currentConversation: null,
      messages: [],
      conversations: [],
      chatSettings: initialChatSettings,

      // Conversation actions
      setCurrentConversation: (conversation) =>
        set(
          { currentConversation: conversation },
          false,
          "setCurrentConversation"
        ),

      // Message actions
      setMessages: (messages) => set({ messages }, false, "setMessages"),

      addMessage: (message) =>
        set(
          (state) => ({ messages: [...state.messages, message] }),
          false,
          "addMessage"
        ),

      updateMessage: (messageId, updates) =>
        set(
          (state) => ({
            messages: state.messages.map((msg) =>
              msg.id === messageId ? { ...msg, ...updates } : msg
            ),
          }),
          false,
          "updateMessage"
        ),

      clearMessages: () => set({ messages: [] }, false, "clearMessages"),

      // Conversations actions
      setConversations: (conversations) =>
        set({ conversations }, false, "setConversations"),

      addConversation: (conversation) =>
        set(
          (state) => ({
            conversations: [conversation, ...state.conversations],
          }),
          false,
          "addConversation"
        ),

      removeConversation: (conversationId) =>
        set(
          (state) => ({
            conversations: state.conversations.filter(
              (c) => c.id !== conversationId
            ),
            currentConversation:
              state.currentConversation?.id === conversationId
                ? null
                : state.currentConversation,
          }),
          false,
          "removeConversation"
        ),

      // Settings actions
      setChatSettings: (settings) =>
        set({ chatSettings: settings }, false, "setChatSettings"),

      // Reset actions
      resetMessages: () => set({ messages: [] }, false, "resetMessages"),

      resetStore: () =>
        set(
          {
            currentConversation: null,
            messages: [],
            conversations: [],
            chatSettings: initialChatSettings,
          },
          false,
          "resetStore"
        ),
    }),
    {
      name: "chat-store",
    }
  )
);

// Selectors for easier access
export const useCurrentConversation = () =>
  useChatStore((state) => state.currentConversation);

export const useMessages = () => useChatStore((state) => state.messages);

export const useConversations = () =>
  useChatStore((state) => state.conversations);

export const useChatSettings = () =>
  useChatStore((state) => state.chatSettings);

// Individual action selectors to avoid hydration issues
export const useSetCurrentConversation = () =>
  useChatStore((state) => state.setCurrentConversation);

export const useSetMessages = () => useChatStore((state) => state.setMessages);

export const useAddMessage = () => useChatStore((state) => state.addMessage);

export const useUpdateMessage = () =>
  useChatStore((state) => state.updateMessage);

export const useClearMessages = () =>
  useChatStore((state) => state.clearMessages);

export const useSetConversations = () =>
  useChatStore((state) => state.setConversations);

export const useAddConversation = () =>
  useChatStore((state) => state.addConversation);

export const useRemoveConversation = () =>
  useChatStore((state) => state.removeConversation);

export const useSetChatSettings = () =>
  useChatStore((state) => state.setChatSettings);

export const useResetMessages = () =>
  useChatStore((state) => state.resetMessages);

export const useResetStore = () => useChatStore((state) => state.resetStore);
