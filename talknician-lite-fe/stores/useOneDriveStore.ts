import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { type OneDriveItem, createOneDriveApiClient } from "@/api/onedrive";

interface OneDriveState {
  // Navigation
  path: string;
  items: OneDriveItem[];
  history: string[];
  // Status
  loading: boolean;
  fileStatuses: Record<string, string>;
  // Actions
  setFileStatus: (fileId: string, status: string) => void;
  navigateToFolder: (
    path: string,
    organizationId: string | undefined,
    accessToken?: string | null
  ) => Promise<void>;
  goBack: () => void;
}

export const useOneDriveStore = create<OneDriveState>()(
  devtools(
    (set, get) => ({
      // Initial State
      path: "/",
      items: [],
      history: [],
      loading: false,
      fileStatuses: {},
      // Actions
      setFileStatus: (fileId, status) =>
        set((state) => ({
          fileStatuses: { ...state.fileStatuses, [fileId]: status },
        })),

      navigateToFolder: async (path, organizationId, accessToken = null) => {
        if (!organizationId) return;
        set({ loading: true });
        try {
          const oneDriveApi = createOneDriveApiClient(accessToken);
          const transformedItems = await oneDriveApi.listOneDriveFiles(path);
          set((state) => ({
            path,
            items: transformedItems,
            history: path === "/" ? [] : [...state.history, state.path],
            loading: false,
          }));
        } catch (error) {
          console.error("Error navigating to folder:", error);
          set({ loading: false });
        }
      },
      goBack: () =>
        set((state) => {
          if (state.history.length > 0) {
            const previousPath = state.history[state.history.length - 1];
            const newHistory = state.history.slice(0, -1);
            return {
              path: previousPath,
              history: newHistory,
              // Note: This assumes items for previousPath are cached on the client
              // A more robust solution might need to refetch or use a more sophisticated cache
            };
          }
          return {};
        }),
    }),
    { name: "OneDriveStore" }
  )
);
