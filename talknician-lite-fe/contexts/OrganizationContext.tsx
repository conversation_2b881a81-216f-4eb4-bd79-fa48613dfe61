"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: string;
  plan?: string;
  description?: string;
  avatar?: string;
  createdAt?: string;
}

interface OrganizationContextType {
  currentOrganization: Organization | null;
  organizations: Organization[];
  setCurrentOrganization: (org: Organization) => void;
  refreshOrganizations: () => Promise<void>;
  isLoading: boolean;
  // Event emitter for organization changes
  onOrganizationChange: (
    callback: (org: Organization | null) => void
  ) => () => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined
);

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [currentOrganization, setCurrentOrganization] =
    useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Event listeners for organization changes
  const [organizationChangeListeners, setOrganizationChangeListeners] =
    useState<Set<(org: Organization | null) => void>>(new Set());

  // Initialize organizations when user is loaded
  useEffect(() => {
    if (isAuthenticated && user?.organizations) {
      setOrganizations(user.organizations);

      // Set current organization from localStorage or first organization
      const savedOrgId = localStorage.getItem("current_organization_id");
      const savedOrg = user.organizations.find((org) => org.id === savedOrgId);

      if (savedOrg) {
        setCurrentOrganization(savedOrg);
      } else if (user.organizations.length > 0) {
        setCurrentOrganization(user.organizations[0]);
        localStorage.setItem(
          "current_organization_id",
          user.organizations[0].id
        );
      }
    } else {
      setOrganizations([]);
      setCurrentOrganization(null);
    }
  }, [isAuthenticated, user]);

  // Notify all listeners when organization changes
  const notifyOrganizationChange = useCallback(
    (org: Organization | null) => {
      organizationChangeListeners.forEach((listener) => listener(org));
    },
    [organizationChangeListeners]
  );

  const handleSetCurrentOrganization = useCallback(
    (org: Organization) => {
      setCurrentOrganization(org);
      localStorage.setItem("current_organization_id", org.id);
      notifyOrganizationChange(org);
    },
    [notifyOrganizationChange]
  );

  // Event listener management
  const onOrganizationChange = useCallback(
    (callback: (org: Organization | null) => void) => {
      setOrganizationChangeListeners((prev) => new Set(prev).add(callback));

      // Return cleanup function
      return () => {
        setOrganizationChangeListeners((prev) => {
          const newSet = new Set(prev);
          newSet.delete(callback);
          return newSet;
        });
      };
    },
    []
  );

  const refreshOrganizations = useCallback(async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const userOrgs = data.data.user.organizations || [];
        setOrganizations(userOrgs);

        // Update current organization if it still exists
        if (currentOrganization) {
          const updatedCurrentOrg = userOrgs.find(
            (org: Organization) => org.id === currentOrganization.id
          );
          if (updatedCurrentOrg) {
            setCurrentOrganization(updatedCurrentOrg);
            notifyOrganizationChange(updatedCurrentOrg);
          } else if (userOrgs.length > 0) {
            handleSetCurrentOrganization(userOrgs[0]);
          } else {
            setCurrentOrganization(null);
            notifyOrganizationChange(null);
            localStorage.removeItem("current_organization_id");
          }
        } else if (userOrgs.length > 0) {
          // If no current org but we have orgs, set the first one
          const savedOrgId = localStorage.getItem("current_organization_id");
          const savedOrg = userOrgs.find(
            (org: Organization) => org.id === savedOrgId
          );

          if (savedOrg) {
            handleSetCurrentOrganization(savedOrg);
          } else {
            handleSetCurrentOrganization(userOrgs[0]);
          }
        }
      }
    } catch (error) {
      console.error("Failed to refresh organizations:", error);
    } finally {
      setIsLoading(false);
    }
  }, [
    isAuthenticated,
    currentOrganization,
    handleSetCurrentOrganization,
    notifyOrganizationChange,
  ]);

  const value: OrganizationContextType = {
    currentOrganization,
    organizations,
    setCurrentOrganization: handleSetCurrentOrganization,
    refreshOrganizations,
    isLoading,
    onOrganizationChange,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider"
    );
  }
  return context;
}
