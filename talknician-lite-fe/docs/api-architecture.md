# Unified API Architecture

## Overview

The frontend now uses a unified API client system that consolidates all API calls through a single, consistent interface. This replaces the previous scattered approach with multiple different API calling patterns.

## Architecture Components

### 1. Unified API Client (`lib/unified-api-client.ts`)

**Core Features:**
- Single base class `UnifiedApiClient` for all API operations
- Consistent error handling and response parsing
- Automatic authentication header injection
- Support for both FormData and JSON requests
- TypeScript-first with proper type safety

**Factory Functions:**
- `createUnifiedApiClient(accessToken)` - For direct token usage
- `useUnifiedApiClient()` - React hook using useAuth context
- `createApiClientWithStoredToken()` - For localStorage token access (backward compatibility)

### 2. API Methods Included

**Persona Management:**
- `getAllPersonas()` - Get all personas for current user/org
- `getPersonaById(id)` - Get specific persona
- `createPersona(data)` - Create new persona
- `updatePersona(id, data)` - Update existing persona
- `deletePersona(id)` - Delete persona

**Chat Management:**
- `getConversations(orgId)` - Get all conversations
- `getConversation(id)` - Get conversation with messages
- `createConversation(title)` - Create new conversation
- `deleteConversation(id)` - Delete conversation
- `getChatSettings(orgId)` - Get chat settings
- `updateChatSettings(orgId, settings)` - Update chat settings

## Migration Summary

### Before (3 different patterns):

1. **chatApi.ts** - Class-based with dependency injection
2. **personaUtils.ts** - Direct fetch with localStorage token
3. **api-client.ts** - Class-based with React Query integration

### After (1 unified pattern):

All API calls now go through the `UnifiedApiClient` class with consistent:
- Authentication handling
- Error management
- Response parsing
- TypeScript types

## Usage Examples

### In React Components (with useAuth):
```typescript
import { useUnifiedApiClient } from "@/lib/unified-api-client";

function MyComponent() {
  const apiClient = useUnifiedApiClient();
  
  const handleCreatePersona = async () => {
    const persona = await apiClient.createPersona({
      title: "My Persona",
      systemPrompt: "You are helpful",
      isPublic: false
    });
  };
}
```

### In Utility Functions (with localStorage token):
```typescript
import { createApiClientWithStoredToken } from "@/lib/unified-api-client";

export async function utilityFunction() {
  const apiClient = createApiClientWithStoredToken();
  return apiClient.getAllPersonas();
}
```

### In Hooks (with direct token):
```typescript
import { createUnifiedApiClient } from "@/lib/unified-api-client";

function useMyHook(accessToken: string) {
  const apiClient = createUnifiedApiClient(accessToken);
  // Use apiClient...
}
```

## Benefits

1. **Consistency** - All API calls follow the same pattern
2. **Maintainability** - Single place to update API logic
3. **Type Safety** - Full TypeScript support with proper types
4. **Error Handling** - Consistent error handling across all endpoints
5. **Authentication** - Centralized auth header management
6. **Testing** - Easier to mock and test API interactions

## Files Updated

- ✅ `lib/unified-api-client.ts` - New unified client
- ✅ `utils/personaUtils.ts` - Updated to use unified client
- ✅ `hooks/usePersonas.ts` - Updated to use unified client
- ✅ `hooks/useHoustonChatSSE.ts` - Updated to use unified client
- ✅ `components/houston/SettingsDialog.tsx` - Updated to use unified client

## Legacy Files (can be deprecated):

- `utils/chatApi.ts` - Replaced by unified client
- Parts of `lib/api-client.ts` - Chat methods moved to unified client

## Next Steps

1. Gradually migrate remaining API calls to use the unified client
2. Remove deprecated API utility files
3. Add more endpoints to the unified client as needed
4. Consider adding React Query integration to the unified client
