"use client";

import { useEffect, useState } from "react";
import Pusher from "pusher-js";
import { usePusher<PERSON><PERSON> } from "@/api/pusher";
import { useOrganization } from "@/contexts/OrganizationContext";

export function usePusher() {
  const [pusher, setPusher] = useState<Pusher | null>(null);
  const { currentOrganization } = useOrganization();
  const pusherApi = usePusherApi();

  useEffect(() => {
    if (!currentOrganization || !process.env.NEXT_PUBLIC_PUSHER_KEY) {
      return;
    }

    const pusherInstance = new Pusher(process.env.NEXT_PUBLIC_PUSHER_KEY, {
      cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER || "ap1",
      authorizer: (channel: any) => {
        return {
          authorize: (
            socketId: string,
            callback: (error: Error | null, authInfo: any) => void
          ) => {
            pusherApi
              .authenticate(socketId, channel.name, currentOrganization.id)
              .then((response) => {
                callback(null, response);
              })
              .catch((error) => {
                callback(error, null);
              });
          },
        };
      },
    });

    setPusher(pusherInstance);

    // Disconnect when the hook is unmounted or dependencies change
    return () => {
      pusherInstance.disconnect();
    };
  }, [currentOrganization, pusherApi]);

  return pusher;
}
