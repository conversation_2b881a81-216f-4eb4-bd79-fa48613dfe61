import { useState, useCallback } from "react";
import { userApi, ChangePasswordData } from "@/api/user";
import { toast } from "sonner";

interface UsePasswordManagementReturn {
  loading: boolean;
  error: string | null;
  changePassword: (data: ChangePasswordData) => Promise<boolean>;
  validatePassword: (password: string) => { valid: boolean; errors: string[] };
}

export const usePasswordManagement = (): UsePasswordManagementReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validatePassword = useCallback((password: string) => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }, []);

  const changePassword = useCallback(
    async (data: ChangePasswordData): Promise<boolean> => {
      try {
        setLoading(true);
        setError(null);

        // Validate passwords match
        if (data.newPassword !== data.confirmPassword) {
          throw new Error("New password and confirmation do not match");
        }

        // Validate new password strength
        const validation = validatePassword(data.newPassword);
        if (!validation.valid) {
          throw new Error(
            `Password requirements not met: ${validation.errors.join(", ")}`
          );
        }

        await userApi.changePassword(data);
        toast.success("Password changed successfully");
        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to change password";
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [validatePassword]
  );

  return {
    loading,
    error,
    changePassword,
    validatePassword,
  };
};
