const {
  azureStorageService,
} = require("./dist/services/azure-storage.service");
const fs = require("fs");
const path = require("path");

async function testAzureStorage() {
  try {
    console.log("Testing Azure Storage Service...");

    // Initialize container
    await azureStorageService.initializeContainer();
    console.log("✅ Container initialized successfully");

    // Create a test file
    const testContent = "Hello, Azure Storage! This is a test file.";
    const testFilePath = path.join(__dirname, "test-file.txt");
    fs.writeFileSync(testFilePath, testContent);

    // Create a mock file object
    const mockFile = {
      originalname: "test-file.txt",
      mimetype: "text/plain",
      size: testContent.length,
      path: testFilePath,
      buffer: Buffer.from(testContent),
    };

    // Test upload
    console.log("Testing file upload...");
    const blobUrl = await azureStorageService.uploadFile(mockFile, "test-org");
    console.log("✅ File uploaded successfully:", blobUrl);

    // Test file exists
    console.log("Testing file existence check...");
    const exists = await azureStorageService.fileExists(blobUrl);
    console.log("✅ File exists check:", exists);

    // Test download
    console.log("Testing file download...");
    const downloadedBuffer = await azureStorageService.downloadFile(blobUrl);
    const downloadedContent = downloadedBuffer.toString();
    console.log("✅ File downloaded successfully. Content:", downloadedContent);

    // Verify content matches
    if (downloadedContent === testContent) {
      console.log("✅ Downloaded content matches original content");
    } else {
      console.log("❌ Downloaded content does not match original content");
    }

    // Test file stream
    console.log("Testing file stream...");
    const stream = await azureStorageService.getFileStream(blobUrl);
    console.log("✅ File stream created successfully");

    // Test delete
    console.log("Testing file deletion...");
    await azureStorageService.deleteFile(blobUrl);
    console.log("✅ File deleted successfully");

    // Verify file no longer exists
    const existsAfterDelete = await azureStorageService.fileExists(blobUrl);
    console.log("✅ File exists after deletion:", existsAfterDelete);

    // Clean up local test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log("✅ Local test file cleaned up");
    }

    console.log("\n🎉 All Azure Storage tests passed!");
  } catch (error) {
    console.error("❌ Azure Storage test failed:", error);
    process.exit(1);
  }
}

// Run the test
testAzureStorage();
