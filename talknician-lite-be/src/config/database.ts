import { PrismaClient } from "@prisma/client";
import { config } from "./index";

// Prisma Client singleton
let prisma: PrismaClient;

declare global {
  var __prisma: PrismaClient | undefined;
}

if (config.isProduction) {
  prisma = new PrismaClient();
} else {
  // In development, use a global variable so the connection is reused
  if (!global.__prisma) {
    global.__prisma = new PrismaClient({
      log: config.isDevelopment ? ["error", "warn"] : ["error"],
    });
  }
  prisma = global.__prisma;
}

// Graceful shutdown
const gracefulShutdown = async () => {
  await prisma.$disconnect();
  process.exit(0);
};

process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

export { prisma };
export default prisma;
