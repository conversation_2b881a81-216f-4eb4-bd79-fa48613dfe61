import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { logger } from "@/utils/logger";

/**
 * Middleware to handle validation errors from express-validator
 */
export const validateRequest = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info(`[VALIDATION] ${req.method} ${req.originalUrl}`);
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }

  next();
};
