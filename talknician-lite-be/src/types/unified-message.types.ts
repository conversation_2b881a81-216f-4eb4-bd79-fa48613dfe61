/* eslint-disable @typescript-eslint/no-explicit-any */
// Types for the new UnifiedMessage system that will replace the current Message model
// This supports rich annotations and references to documents and websites

import {
  MessageAnnotation,
  MessageReference,
  $Enums,
  MessageDocument,
} from "@prisma/client";

// Re-export Prisma types for convenience
export { MessageAnnotation, MessageReference };

// Types for OpenAI response parsing
export interface OpenAIAnnotation {
  type: "url_citation" | "file_citation";
  start_index?: number;
  end_index?: number;
  index?: number;
  url?: string;
  title?: string;
  file_id?: string;
  filename?: string;
}

export interface OpenAIContentPart {
  type: "output_text";
  text: string;
  annotations?: OpenAIAnnotation[];
  logprobs?: any[];
}

export interface OpenAIResponseEvent {
  type: string;
  sequence_number?: number;
  item_id?: string;
  output_index?: number;
  content_index?: number;
  part?: OpenAIContentPart;
}

// Database creation types (for service layer)
export interface CreateUnifiedMessageData {
  role: "user" | "assistant" | "system" | "developer";
  content: string;
  conversationId: string;
  userId: string;
  annotations?: Omit<MessageAnnotation, "id" | "unifiedMessageId">[];
  references?: Omit<
    MessageReference,
    "id" | "createdAt" | "unifiedMessageId"
  >[];
  openaiMessageId?: string;
  openaiItemId?: string;
  sequenceNumber?: number;
  metadata?: Record<string, any>;
  messageDocuments?: MessageDocument[];
}

// Re-export Prisma enums for convenience
export { $Enums };

// Frontend-compatible annotation type
export interface FrontendMessageAnnotation {
  id: string;
  type: "url_citation" | "file_citation";
  startIndex: number;
  endIndex: number;
  url?: string;
  title?: string;
  fileId?: string;
  filename?: string;
  documentId?: string;
  websiteId?: string;
}

// Frontend-compatible reference type
export interface FrontendMessageReference {
  id: string;
  type: "document" | "website";
  title: string;
  documentId?: string;
  azureBlobUrl?: string;
  openaiFileId?: string;
  filename?: string;
  websiteId?: string;
  url?: string;
  scrapedContent?: string;
  metadata?: any;
  createdAt: string;
}

// Response types for API (frontend-compatible format)
export interface UnifiedMessageResponse {
  id: string;
  role: string;
  content: string;
  conversationId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  annotations: FrontendMessageAnnotation[];
  references: FrontendMessageReference[];
  openaiMessageId?: string;
  openaiItemId?: string;
  sequenceNumber?: number;
  metadata?: any; // JsonValue from Prisma can be any
}

// Helper types for frontend
export interface SourceReference {
  type: "document" | "website";
  title: string;
  content?: string;
  url?: string;
  documentId?: string;
  websiteId?: string;
  azureBlobUrl?: string;
  filename?: string;
}

// Conversion utilities type
export interface AnnotationWithReference extends MessageAnnotation {
  reference?: MessageReference;
}
