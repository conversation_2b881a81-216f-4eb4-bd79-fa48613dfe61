import OpenAI from "openai";
import { logger } from "../utils/logger";
import { prisma } from "../config/database";
import { azureStorageService } from "./azure-storage.service";
import fs from "fs";
import path from "path";
import * as os from "os";

export interface ScrapedContent {
  url: string;
  title: string;
  content: string;
  markdown: string;
  metadata: {
    description?: string;
    keywords?: string[];
    author?: string;
    publishedDate?: string;
    wordCount?: number;
    language?: string;
  };
}

const EXTRACTION_SCHEMA = {
  title: "PageContent",
  type: "object",
  properties: {
    title: {
      description: "The main title of the webpage.",
      title: "Title",
      type: "string",
    },
    summary: {
      description:
        "A concise summary of the page content, in 2-3 sentences, focusing on the key information for a RAG system.",
      title: "Summary",
      type: "string",
    },
    keywords: {
      description: "A list of 5-10 relevant SEO-style keywords.",
      items: { type: "string" },
      title: "Keywords",
      type: "array",
    },
  },
  required: ["title", "summary", "keywords"],
};

interface CrawlJobResult {
  url: string;
  extracted_content: string; // This will be a JSON string of an array
  markdown: string;
}

interface ExtractedPageContent {
  title: string;
  summary: string;
  keywords: string[];
}

interface CrawlJobResponse {
  results: CrawlJobResult[];
}

export class WebScrapingService {
  private openai: OpenAI;
  private crawl4aiUrl =
    process.env.CRAWL4AI_SERVICE_URL || "http://localhost:8010";

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Scrape a website using Crawl4AI service
   */
  async scrapeWebsite(url: string): Promise<ScrapedContent> {
    try {
      logger.info(`Scraping website with Crawl4AI: ${url}`);

      const response = await fetch(`${this.crawl4aiUrl}/crawl`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(
          `Crawl4AI service error: ${response.status} - ${errorBody}`
        );
      }

      const data = (await response.json()) as { content: string };
      const markdownContent = data.content;

      if (!markdownContent) {
        throw new Error("Failed to scrape website: no content returned.");
      }

      // Try to extract title from markdown (e.g., from the first H1)
      const titleMatch = markdownContent.match(/^#\s+(.*)/);
      const title = titleMatch ? titleMatch[1] : new URL(url).hostname;

      return {
        url: url,
        title: title,
        content: markdownContent, // Using markdown content as the main content
        markdown: markdownContent,
        metadata: {
          wordCount: markdownContent.split(/\s+/).length,
        },
      };
    } catch (error) {
      logger.error(`Error scraping website ${url}:`, error);
      throw new Error(
        `Failed to scrape website: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Adds a website to the RAG system by performing a crawl job (deep or sitemap)
   * and ingesting each page as a separate document. This method calls the official
   * crawl4ai container API.
   */
  async addWebsiteToRAGV2(
    url: string,
    strategy: "deep" | "sitemap",
    max_depth: number,
    organizationId: string,
    userId: string
  ): Promise<{ success: boolean; documentIds: string[]; message: string }> {
    try {
      logger.info(
        `Starting official crawl job for ${url} with strategy ${strategy}`
      );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const payload: any = {
        urls: [url],
        config: {
          extraction_strategy: {
            name: "LLMExtractionStrategy",
            params: {
              llm_config: {
                provider: "openai/gpt-4o-mini",
                api_key_env: "OPENAI_API_KEY",
              },
              schema: EXTRACTION_SCHEMA,
              extraction_type: "schema",
            },
          },
        },
      };

      if (strategy === "deep") {
        payload.config.deep_crawl_strategy = {
          name: "BFSDeePCrawlStrategy",
          params: {
            max_depth: max_depth,
          },
        };
      } else {
        // sitemap strategy
        const sitemapUrl = new URL("/sitemap.xml", url).href;
        payload.config.routify_strategy = {
          name: "SitemapRoutifyStrategy",
          params: {
            sitemap_url: sitemapUrl,
          },
        };
      }

      const crawlResponse = await fetch(this.crawl4aiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!crawlResponse.ok) {
        const errorBody = await crawlResponse.text();
        throw new Error(
          `Crawl4AI service error: ${crawlResponse.status} - ${errorBody}`
        );
      }

      const crawlData = (await crawlResponse.json()) as CrawlJobResponse;
      const documentIds: string[] = [];

      logger.info(
        `Crawl job finished. Found ${crawlData.results.length} pages to process.`
      );

      for (const page of crawlData.results) {
        if (!page.extracted_content) {
          logger.warn(`Skipping page ${page.url} due to missing content.`);
          continue;
        }

        try {
          // The extracted_content is a string containing a JSON array.
          const extractedArray = JSON.parse(
            page.extracted_content
          ) as ExtractedPageContent[];

          // We only care about the first match on the page for this use case.
          if (extractedArray.length === 0 || !extractedArray[0].summary) {
            logger.warn(
              `Skipping page ${page.url} because CSS selector did not find content.`
            );
            continue;
          }
          const content = extractedArray[0];
          const { title, summary } = content;

          const tempFilePath = path.join(
            os.tmpdir(),
            `doc_${Date.now()}_${Math.random()}.txt`
          );
          fs.writeFileSync(tempFilePath, summary);

          const stats = fs.statSync(tempFilePath);
          const document = await prisma.document.create({
            data: {
              filename: `${title}.txt`,
              originalName: `${title}.txt`,
              mimeType: "text/plain",
              size: stats.size,
              localPath: tempFilePath,
              userId,
              uploadedBy: userId,
              organizationId,
              status: "PROCESSING",
              scrapedWebsite: {
                create: {
                  url: page.url,
                  title,
                  content: summary,
                  markdown: page.markdown,
                  metadata: { keywords: [] }, // No keywords from this strategy
                  status: "PROCESSING",
                  userId,
                  organizationId,
                },
              },
            },
            include: {
              scrapedWebsite: true,
            },
          });
          documentIds.push(document.id);
          logger.info(
            `Created document ${document.id} for crawled page ${page.url}`
          );
        } catch (parseError) {
          logger.error(
            `Failed to parse extracted_content for ${page.url}:`,
            parseError
          );
          continue;
        }
      }

      if (documentIds.length === 0) {
        return {
          success: false,
          documentIds: [],
          message:
            "Crawl completed, but no pages contained content that could be processed.",
        };
      }

      // Note: In a real-world scenario, you might trigger a background job here
      // to upload these files to OpenAI and update their status.
      // For now, we return success.

      return {
        success: true,
        documentIds,
        message: `Successfully created ${documentIds.length} documents from the website crawl.`,
      };
    } catch (error) {
      logger.error("Error in addWebsiteToRAGV2:", error);
      throw new Error(
        `Failed to add website to RAG: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Ensures an organization has a vector store, creating one if it doesn't.
   * @param organizationId The ID of the organization.
   * @returns The organization object with a vectorStoreId.
   */
  private async ensureVectorStoreForOrg(organizationId: string) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new Error("Organization not found.");
    }

    if (!organization.vectorStoreId) {
      const vectorStore = await this.openai.vectorStores.create({
        name: `${organization.name || "Organization"} Knowledge Base`,
      });

      return prisma.organization.update({
        where: { id: organizationId },
        data: { vectorStoreId: vectorStore.id },
      });
    }
    return organization;
  }

  /**
   * Validate if a URL is accessible and scrapable using Crawl4AI service
   */
  async validateUrl(url: string): Promise<{
    accessible: boolean;
    canScrape: boolean;
    title?: string;
    error?: string;
  }> {
    try {
      logger.info(`Validating URL with Crawl4AI: ${url}`);

      const response = await fetch(`${this.crawl4aiUrl}/validate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url,
          word_count_threshold: 1,
        }),
      });

      if (!response.ok) {
        throw new Error(`Crawl4AI service error: ${response.status}`);
      }

      const data = await response.json();

      const responseData = data as any;
      return {
        accessible: responseData.accessible,
        canScrape: responseData.can_scrape,
        title: responseData.title,
        error: responseData.error,
      };
    } catch (error) {
      logger.error(`Error validating URL ${url}:`, error);
      return {
        accessible: false,
        canScrape: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Extract text content from HTML using simple regex patterns (fallback method)
   */
  private extractTextContent(html: string): string {
    // Remove script and style elements
    let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "");
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "");

    // Remove HTML tags
    text = text.replace(/<[^>]*>/g, " ");

    // Decode HTML entities
    text = text.replace(/&nbsp;/g, " ");
    text = text.replace(/&amp;/g, "&");
    text = text.replace(/&lt;/g, "<");
    text = text.replace(/&gt;/g, ">");
    text = text.replace(/&quot;/g, '"');
    text = text.replace(/&#39;/g, "'");

    // Clean up whitespace
    text = text.replace(/\s+/g, " ");
    text = text.trim();

    return text;
  }

  /**
   * Extract title from HTML
   */
  private extractTitle(html: string): string | null {
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    if (titleMatch) {
      return titleMatch[1].trim();
    }
    return null;
  }

  /**
   * Add scraped website to RAG system
   */
  async addWebsiteToRAG(
    url: string,
    organizationId: string,
    userId: string
  ): Promise<{ success: boolean; documentId?: string; message: string }> {
    try {
      logger.info(
        `Adding website to RAG: ${url} for organization: ${organizationId}`
      );

      // Check if URL already exists by checking filename pattern
      const urlHostname = new URL(url).hostname;
      const existingDoc = await prisma.document.findFirst({
        where: {
          organizationId,
          filename: {
            contains: `website_${urlHostname}`,
          },
        },
      });

      if (existingDoc) {
        return {
          success: false,
          message: "Website already exists in the knowledge base",
        };
      }

      // Scrape the website
      const scrapedContent = await this.scrapeWebsite(url);

      // Create a text file with the scraped content
      const contentHostname = new URL(scrapedContent.url).hostname;
      const fileName = `website_${contentHostname}_${Date.now()}.txt`;
      const fileContent = `Title: ${scrapedContent.title}\nURL: ${scrapedContent.url}\n\n${scrapedContent.content}`;

      // Create temporary file for Azure upload
      const tempDir = "temp";
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempPath = path.join(tempDir, fileName);
      fs.writeFileSync(tempPath, fileContent, "utf-8");

      // Create file object for Azure upload
      const tempFile = {
        path: tempPath,
        originalname: fileName,
        mimetype: "text/plain",
        size: Buffer.byteLength(fileContent, "utf-8"),
      } as Express.Multer.File;

      // Upload to Azure Storage
      const azureUrl = await azureStorageService.uploadFile(
        tempFile,
        organizationId
      );

      // Create document record
      const document = await prisma.document.create({
        data: {
          filename: fileName,
          originalName: scrapedContent.title,
          size: Buffer.byteLength(fileContent, "utf-8"),
          mimeType: "text/plain",
          azureBlobUrl: azureUrl,
          organizationId,
          userId,
          uploadedBy: userId,
          status: "PROCESSING",
        },
      });

      // Get organization to check for vector store
      const organization = await this.ensureVectorStoreForOrg(organizationId);

      if (!organization?.vectorStoreId) {
        // Create vector store if it doesn't exist
        const vectorStore = await this.openai.vectorStores.create({
          name: `${organization?.name || "Organization"} Knowledge Base`,
          expires_after: {
            anchor: "last_active_at",
            days: 365,
          },
        });

        await prisma.organization.update({
          where: { id: organizationId },
          data: { vectorStoreId: vectorStore.id },
        });

        organization!.vectorStoreId = vectorStore.id;
      }

      // Create a File object for OpenAI upload
      const fileForOpenAI = new File([fileContent], fileName, {
        type: "text/plain",
      });

      // Upload file to OpenAI
      const openaiFile = await this.openai.files.create({
        file: fileForOpenAI,
        purpose: "assistants",
      });

      // Add file to vector store
      await this.openai.vectorStores.files.create(organization!.vectorStoreId, {
        file_id: openaiFile.id,
      });

      // Update document with OpenAI file ID and mark as completed
      await prisma.document.update({
        where: { id: document.id },
        data: {
          openaiFileId: openaiFile.id,
          status: "COMPLETED",
        },
      });

      logger.info(`Successfully added website to RAG: ${url}`);

      return {
        success: true,
        documentId: document.id,
        message: "Website successfully added to knowledge base",
      };
    } catch (error) {
      logger.error(`Error adding website to RAG:`, error);
      return {
        success: false,
        message: `Failed to add website: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  /**
   * Validate URL format
   */
  isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === "http:" || urlObj.protocol === "https:";
    } catch {
      return false;
    }
  }
}

export const webScrapingService = new WebScrapingService();
