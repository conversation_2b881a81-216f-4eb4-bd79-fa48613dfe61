import { Prisma } from "@prisma/client";
import prisma from "../config/database";

/**
 * Creates a new message document.
 * @param data - The data for the new message document.
 * @returns The created message document.
 */
export const createMessageDocument = async (
  data: Prisma.MessageDocumentUncheckedCreateInput
) => {
  return prisma.messageDocument.create({
    data,
  });
};

/**
 * Retrieves a message document by its ID.
 * @param id - The ID of the message document to retrieve.
 * @returns The message document, or null if not found.
 */
export const getMessageDocumentById = async (id: string) => {
  return prisma.messageDocument.findUnique({
    where: { id },
  });
};

/**
 * Retrieves all message documents for a specific message.
 * @param messageId - The ID of the message.
 * @returns A list of message documents.
 */
export const getMessageDocumentsByMessageId = async (messageId: string) => {
  return prisma.messageDocument.findMany({
    where: { messageId },
  });
};

/**
 * Updates a message document.
 * @param id - The ID of the message document to update.
 * @param data - The data to update.
 * @returns The updated message document.
 */
export const updateMessageDocument = async (
  id: string,
  data: Prisma.MessageDocumentUpdateInput
) => {
  return prisma.messageDocument.update({
    where: { id },
    data,
  });
};

/**
 * Deletes a message document.
 * @param id - The ID of the message document to delete.
 * @returns The deleted message document.
 */
export const deleteMessageDocument = async (id: string) => {
  return prisma.messageDocument.delete({
    where: { id },
  });
};
