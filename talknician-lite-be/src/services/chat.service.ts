import OpenAI from "openai";
import {
  MessageDocument,
  MessageRole,
  PrismaClient,
  ConversationVisibility,
} from "@prisma/client";
import { logger } from "../utils/logger";
import {
  ResponseCreateParamsNonStreaming,
  ResponseCreateParamsStreaming,
  ResponseInputItem,
} from "openai/resources/responses/responses";
import { OpenAIResponseEvent } from "@/types/unified-message.types";
import {
  unifiedMessageService,
  UnifiedMessageWithRelations,
} from "./unified-message.service";
import { sseService } from "./sse.service";

const prisma = new PrismaClient();

interface ChatSettings {
  enableWebSearch?: boolean;
  personality?: string;
  systemPrompt?: string;
  documentSearch?: boolean;
}

export class ChatService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async sendMessage(
    conversationId: string,
    userInput: string,
    userId: string,
    organizationId: string,
    settings: ChatSettings = {},
    files?: MessageDocument[]
  ): Promise<UnifiedMessageWithRelations | null> {
    let finalUnifiedMessage = null;
    const messages = await prisma.unifiedMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: "asc" },
      include: {
        messageDocuments: true,
      },
    });

    //convert messages to OpenAI format
    const formattedMessages: ResponseInputItem[] = messages.map((message) => {
      if (message.messageDocuments && message.messageDocuments.length > 0) {
        const filesInput = message.messageDocuments.map((doc) => {
          const isImage = doc.mimeType.includes("image");
          return {
            type: isImage ? "input_image" : "input_file",
            file_id: doc.openaiFileId,
          };
        });
        return {
          role: message.role as MessageRole,
          content: [
            ...filesInput,
            { type: "input_text", text: message.content },
          ],
        } as ResponseInputItem;
      }

      return {
        role: message.role as MessageRole,
        content: message.content,
      };
    });

    console.log("settings", settings);
    //Document search
    let vector_store_id = null;
    if (settings.documentSearch) {
      const organization = await prisma.organization.findFirst({
        where: { id: organizationId },
        select: { vectorStoreId: true },
      });
      vector_store_id = organization?.vectorStoreId;
      console.log("organization", organization);
      console.log("vector_store_id", vector_store_id);
    }
    const tools: ResponseCreateParamsNonStreaming["tools"] = [];
    if (vector_store_id) {
      console.log("vector_store_id", vector_store_id);
      tools.push({ type: "file_search", vector_store_ids: [vector_store_id] });
    }

    //Web search
    if (settings.enableWebSearch) {
      tools.push({ type: "web_search_preview" });
    }

    //Personality
    if (settings.personality) {
      formattedMessages.unshift({
        role: MessageRole.developer,
        content: settings.personality,
      });
    }

    //Save user message to UnifiedMessage
    await unifiedMessageService.createUnifiedMessage({
      role: MessageRole.user,
      content: userInput,
      conversationId,
      userId,
      messageDocuments: files,
    });

    // Test UnifiedMessage system with file search
    let messageWithFiles = null;
    if (files && files?.length > 0) {
      const filesInput = files.map((file) => {
        const isImage = file.mimeType.includes("image");
        return {
          type: isImage ? "input_image" : "input_file",
          file_id: file.openaiFileId,
        };
      });
      messageWithFiles = [
        ...filesInput,
        { type: "input_text", text: userInput },
      ];
    }

    const openAIOptions: ResponseCreateParamsStreaming = {
      model: "gpt-4.1",
      instructions:
        "You are Houston, an AI assistant for Talknician. You help users with their questions and tasks.",
      input: [
        ...formattedMessages,
        {
          role: MessageRole.user,
          content: (messageWithFiles as any) || userInput,
        },
      ],
      stream: true,
    };

    if (tools.length > 0) {
      openAIOptions.tools = tools;
    }
    const response = await this.openai.responses.create(openAIOptions);

    let finalResponse = "";

    for await (const event of response) {
      if (event.type === "response.output_text.delta") {
        finalResponse += event.delta;
        sseService.sendContent(conversationId, userId, finalResponse);
      } else if (event.type === "response.file_search_call.in_progress") {
        sseService.sendToolCallsStarted(
          conversationId,
          userId,
          "Searching documents"
        );
      } else if (event.type === "response.web_search_call.in_progress") {
        sseService.sendToolCallsStarted(
          conversationId,
          userId,
          "Searching the web"
        );
      } else if (event.type === "response.content_part.done") {
        console.log("=== Content Part Done ===");
        console.log("Event:", JSON.stringify(event, null, 2));

        try {
          finalUnifiedMessage =
            await unifiedMessageService.createFromOpenAIResponse(
              event as OpenAIResponseEvent,
              conversationId,
              userId
            );

          if (finalUnifiedMessage) {
            if (finalUnifiedMessage.annotations.length > 0) {
              console.log("=== File Citations ===");
              finalUnifiedMessage.annotations.forEach((ann, idx) => {
                console.log(`Annotation ${idx + 1}:`, {
                  type: ann.type,
                  startIndex: ann.startIndex,
                  endIndex: ann.endIndex,
                  fileId: ann.fileId,
                  filename: ann.filename,
                  documentId: ann.documentId,
                });
              });
            }

            if (finalUnifiedMessage.references.length > 0) {
              console.log("=== Document References ===");
              finalUnifiedMessage.references.forEach((ref, idx) => {
                console.log(`Reference ${idx + 1}:`, {
                  type: ref.type,
                  title: ref.title,
                  filename: ref.filename,
                  documentId: ref.documentId,
                  azureBlobUrl: ref.azureBlobUrl,
                  openaiFileId: ref.openaiFileId,
                });
              });
            }
            sseService.sendCompletion(
              conversationId,
              userId,
              finalUnifiedMessage
            );
          }
        } catch (error) {
          logger.error("Error creating unified message:", error);
          sseService.sendError(
            conversationId,
            userId,
            "Failed to create unified message"
          );
        }
      }
    }
    console.log("Final response length:", finalUnifiedMessage);
    return finalUnifiedMessage;
  }

  /**
   * Get conversation with messages (with permission check)
   */
  async getConversation(
    conversationId: string,
    userId: string,
    organizationId: string
  ) {
    // First check if user can access this conversation
    const canAccess = await this.canAccessConversation(
      conversationId,
      userId,
      organizationId
    );
    if (!canAccess) {
      return null;
    }

    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        unifiedMessages: {
          orderBy: { createdAt: "asc" },
          include: {
            references: true,
            annotations: true,
            messageDocuments: true,
          },
        },
      },
    });
    console.log("Conversation:", conversation);
    return conversation;
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    userId: string,
    organizationId: string,
    title?: string,
    visibility: ConversationVisibility = ConversationVisibility.private
  ) {
    return await prisma.conversation.create({
      data: {
        userId,
        organizationId,
        title: title || "New Conversation",
        visibility,
      },
    });
  }

  /**
   * List conversations for a user in an organization with visibility filtering
   */
  async listConversations(
    userId: string,
    organizationId: string,
    filter?: "my-chats" | "public-chats" | "all",
    authorId?: string
  ) {
    let whereClause: any = {
      organizationId,
      isArchived: false,
    };

    // Apply author filtering first if specified
    if (authorId) {
      whereClause.userId = authorId;
      // If filtering by specific author, only show conversations user can access
      if (authorId !== userId) {
        // Can only see public conversations from other authors
        whereClause.visibility = ConversationVisibility.public;
      }
    } else {
      // Apply visibility filtering
      if (filter === "my-chats") {
        // Only user's own conversations
        whereClause.userId = userId;
      } else if (filter === "public-chats") {
        // Only public conversations from other users
        whereClause.visibility = ConversationVisibility.public;
        whereClause.userId = { not: userId };
      } else if (filter === "all") {
        // All conversations user can access (own + public from others)
        whereClause.OR = [
          { userId }, // User's own conversations (private or public)
          {
            visibility: ConversationVisibility.public,
            userId: { not: userId },
          }, // Public conversations from others
        ];
      } else {
        // Default: user's own conversations + public conversations from others
        whereClause.OR = [
          { userId }, // User's own conversations
          {
            visibility: ConversationVisibility.public,
            userId: { not: userId },
          }, // Public conversations from others
        ];
      }
    }

    return await prisma.conversation.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
      orderBy: { updatedAt: "desc" },
      take: 50,
    });
  }

  /**
   * Archive a conversation
   */
  async archiveConversation(conversationId: string, userId: string) {
    return await prisma.conversation.update({
      where: {
        id: conversationId,
        userId,
      },
      data: {
        isArchived: true,
      },
    });
  }

  /**
   * Check if user can access a conversation
   */
  async canAccessConversation(
    conversationId: string,
    userId: string,
    organizationId: string
  ): Promise<boolean> {
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        organizationId,
      },
    });

    if (!conversation) {
      return false;
    }

    // User can access if they own the conversation or if it's public
    return (
      conversation.userId === userId ||
      conversation.visibility === ConversationVisibility.public
    );
  }

  /**
   * Check if user can edit a conversation (only owner can edit)
   */
  async canEditConversation(
    conversationId: string,
    userId: string
  ): Promise<boolean> {
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        userId,
      },
    });

    return !!conversation;
  }

  /**
   * Update conversation visibility (only owner can update)
   */
  async updateConversationVisibility(
    conversationId: string,
    userId: string,
    visibility: ConversationVisibility
  ) {
    return await prisma.conversation.update({
      where: {
        id: conversationId,
        userId, // Ensure only owner can update
      },
      data: {
        visibility,
      },
    });
  }

  /**
   * Get organization members who have created conversations
   */
  async getConversationAuthors(organizationId: string, currentUserId: string) {
    const authors = await prisma.conversation.findMany({
      where: {
        organizationId,
        isArchived: false,
        OR: [
          { userId: currentUserId }, // User's own conversations
          { visibility: ConversationVisibility.public }, // Public conversations
        ],
      },
      select: {
        userId: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
      distinct: ["userId"],
      orderBy: {
        user: {
          name: "asc",
        },
      },
    });

    return authors.map((author) => author.user);
  }

  /**
   * Get user's chat settings (simplified - only web search preference)
   */
  async getChatSettings(): Promise<ChatSettings> {
    try {
      // Since ChatSettings model is removed, we'll store web search preference
      // in user preferences or return default for now
      // TODO: Implement user preferences storage if needed
      return {
        enableWebSearch: false,
        personality: undefined,
        systemPrompt: undefined,
        documentSearch: true, // Always enable document search
      };
    } catch (error) {
      logger.error("Error getting chat settings:", error);
      // Return default settings on error
      return {
        enableWebSearch: false,
        personality: undefined,
        systemPrompt: undefined,
        documentSearch: true,
      };
    }
  }

  /**
   * Update user's chat settings (simplified - only web search preference)
   */
  async updateChatSettings(
    userId: string,
    organizationId: string,
    settings: Partial<ChatSettings>
  ): Promise<ChatSettings> {
    try {
      logger.info(`Updating chat settings for user ${userId}:`, settings);

      // Since ChatSettings model is removed, we'll store web search preference
      // in user preferences or return updated settings for now
      // TODO: Implement user preferences storage if needed
      return {
        enableWebSearch: settings.enableWebSearch ?? false,
        personality: settings.personality,
        systemPrompt: settings.systemPrompt,
        documentSearch: settings.documentSearch ?? true,
      };
    } catch (error) {
      logger.error("Error updating chat settings:", error);
      throw new Error("Failed to update chat settings");
    }
  }
}

export const chatService = new ChatService();
export default chatService;
