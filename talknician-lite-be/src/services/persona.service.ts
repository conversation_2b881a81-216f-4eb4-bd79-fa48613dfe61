import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

export interface CreatePersonaData {
  title: string;
  systemPrompt: string;
  isPublic?: boolean;
}

export interface UpdatePersonaData {
  title?: string;
  systemPrompt?: string;
  isPublic?: boolean;
}

export interface PersonaWithRelations {
  id: string;
  title: string;
  systemPrompt: string;
  userId: string | null;
  organizationId: string;
  isPublic: boolean;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name: string | null;
    email: string;
  } | null;
}

export class PersonaService {
  /**
   * Create default personas for an organization
   */
  async createDefaultPersonas(organizationId: string): Promise<PersonaWithRelations[]> {
    try {
      const defaultPersonas = [
        {
          title: "Short & Concise",
          systemPrompt: "You are a helpful AI assistant that provides brief, direct answers. Keep responses concise and to the point while maintaining accuracy and helpfulness.",
          organizationId,
          isDefault: true,
          isPublic: true,
        },
        {
          title: "Detailed & Thorough", 
          systemPrompt: "You are a comprehensive AI assistant that provides detailed, thorough explanations. Include relevant context, examples, and step-by-step guidance when helpful.",
          organizationId,
          isDefault: true,
          isPublic: true,
        },
        {
          title: "Creative & Friendly",
          systemPrompt: "You are a creative and friendly AI assistant with an engaging personality. Use a warm, conversational tone and feel free to be creative in your responses while remaining helpful and accurate.",
          organizationId,
          isDefault: true,
          isPublic: true,
        },
      ];

      const createdPersonas = await Promise.all(
        defaultPersonas.map((persona) =>
          prisma.persona.create({
            data: persona,
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          })
        )
      );

      logger.info(`Created ${createdPersonas.length} default personas for organization ${organizationId}`);
      return createdPersonas;
    } catch (error) {
      logger.error("Error creating default personas:", error);
      throw new Error("Failed to create default personas");
    }
  }

  /**
   * Get all personas for a user in an organization
   */
  async getPersonas(userId: string, organizationId: string): Promise<PersonaWithRelations[]> {
    try {
      const personas = await prisma.persona.findMany({
        where: {
          organizationId,
          OR: [
            { userId }, // User's own personas
            { isPublic: true }, // Public personas
            { isDefault: true }, // Default personas
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: [
          { isDefault: "desc" }, // Default personas first
          { isPublic: "desc" }, // Then public personas
          { createdAt: "asc" }, // Then by creation date
        ],
      });

      return personas;
    } catch (error) {
      logger.error("Error getting personas:", error);
      throw new Error("Failed to get personas");
    }
  }

  /**
   * Get a specific persona by ID
   */
  async getPersonaById(personaId: string, userId: string, organizationId: string): Promise<PersonaWithRelations | null> {
    try {
      const persona = await prisma.persona.findFirst({
        where: {
          id: personaId,
          organizationId,
          OR: [
            { userId }, // User's own persona
            { isPublic: true }, // Public persona
            { isDefault: true }, // Default persona
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return persona;
    } catch (error) {
      logger.error("Error getting persona by ID:", error);
      throw new Error("Failed to get persona");
    }
  }

  /**
   * Create a new persona
   */
  async createPersona(
    userId: string,
    organizationId: string,
    data: CreatePersonaData
  ): Promise<PersonaWithRelations> {
    try {
      const persona = await prisma.persona.create({
        data: {
          title: data.title,
          systemPrompt: data.systemPrompt,
          userId,
          organizationId,
          isPublic: data.isPublic || false,
          isDefault: false,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info(`Created persona ${persona.id} for user ${userId}`);
      return persona;
    } catch (error) {
      logger.error("Error creating persona:", error);
      throw new Error("Failed to create persona");
    }
  }

  /**
   * Update a persona
   */
  async updatePersona(
    personaId: string,
    userId: string,
    organizationId: string,
    data: UpdatePersonaData
  ): Promise<PersonaWithRelations> {
    try {
      // Check if user owns this persona or if it's editable
      const existingPersona = await prisma.persona.findFirst({
        where: {
          id: personaId,
          organizationId,
          userId, // Only allow updating own personas
          isDefault: false, // Don't allow updating default personas
        },
      });

      if (!existingPersona) {
        throw new Error("Persona not found or not editable");
      }

      const persona = await prisma.persona.update({
        where: { id: personaId },
        data: {
          ...(data.title && { title: data.title }),
          ...(data.systemPrompt && { systemPrompt: data.systemPrompt }),
          ...(data.isPublic !== undefined && { isPublic: data.isPublic }),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info(`Updated persona ${persona.id} for user ${userId}`);
      return persona;
    } catch (error) {
      logger.error("Error updating persona:", error);
      throw new Error("Failed to update persona");
    }
  }

  /**
   * Delete a persona
   */
  async deletePersona(personaId: string, userId: string, organizationId: string): Promise<void> {
    try {
      // Check if user owns this persona and it's not a default persona
      const existingPersona = await prisma.persona.findFirst({
        where: {
          id: personaId,
          organizationId,
          userId, // Only allow deleting own personas
          isDefault: false, // Don't allow deleting default personas
        },
      });

      if (!existingPersona) {
        throw new Error("Persona not found or not deletable");
      }

      await prisma.persona.delete({
        where: { id: personaId },
      });

      logger.info(`Deleted persona ${personaId} for user ${userId}`);
    } catch (error) {
      logger.error("Error deleting persona:", error);
      throw new Error("Failed to delete persona");
    }
  }

  /**
   * Ensure default personas exist for an organization
   */
  async ensureDefaultPersonas(organizationId: string): Promise<void> {
    try {
      const existingDefaults = await prisma.persona.findMany({
        where: {
          organizationId,
          isDefault: true,
        },
      });

      if (existingDefaults.length === 0) {
        await this.createDefaultPersonas(organizationId);
      }
    } catch (error) {
      logger.error("Error ensuring default personas:", error);
      throw new Error("Failed to ensure default personas");
    }
  }
}

export const personaService = new PersonaService();
export default personaService;
