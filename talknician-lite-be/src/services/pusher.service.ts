import Pusher from "pusher";
import logger from "@/utils/logger";

const PUSHER_APP_ID = process.env.PUSHER_APP_ID;
const PUSHER_KEY = process.env.PUSHER_KEY;
const PUSHER_SECRET = process.env.PUSHER_SECRET;
const PUSHER_CLUSTER = process.env.PUSHER_CLUSTER;

if (!PUSHER_APP_ID || !PUSHER_KEY || !PUSHER_SECRET || !PUSHER_CLUSTER) {
  logger.warn(
    "Pusher credentials are not fully configured. Real-time updates will be disabled."
  );
}

export const pusher =
  PUSHER_APP_ID && PUSHER_KEY && PUSHER_SECRET && PUSHER_CLUSTER
    ? new Pusher({
        appId: PUSHER_APP_ID,
        key: PUSHER_KEY,
        secret: PUSHER_SECRET,
        cluster: PUSHER_CLUSTER,
        useTLS: true,
      })
    : null;

export const triggerPusherEvent = (
  channel: string,
  event: string,
  data: any
) => {
  if (pusher) {
    pusher.trigger(channel, event, data).catch((error) => {
      logger.error("Failed to trigger Pusher event", {
        channel,
        event,
        error,
      });
    });
  }
};
