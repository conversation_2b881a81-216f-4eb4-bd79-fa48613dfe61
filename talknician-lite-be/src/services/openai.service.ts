import OpenAI, { toFile } from "openai";
import { azureStorageService } from "./azure-storage.service";
import { logger } from "@/utils/logger";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Uploads a file from Azure Blob Storage to OpenAI.
 *
 * @param azureBlobUrl - The URL of the file in Azure Blob Storage.
 * @param originalname - The original name of the file.
 * @returns The OpenAI file object.
 */
const uploadFileFromAzureToOpenAI = async (
  azureBlobUrl: string,
  originalname: string
) => {
  try {
    // Get file stream from Azure Storage
    const fileStream = await azureStorageService.getFileStream(azureBlobUrl);

    // Upload file to OpenAI
    const openaiFile = await openai.files.create({
      file: await toFile(fileStream, originalname),
      purpose: "assistants",
    });

    return openaiFile;
  } catch (error) {
    logger.error("Error uploading file from Azure to OpenAI:", error);
    throw new Error("Failed to upload file to OpenAI.");
  }
};

export { openai, uploadFileFromAzureToOpenAI };
