import express from "express";
import { Request, Response } from "express";
import { authenticate } from "../middleware/auth.middleware";
import { personaService } from "../services/persona.service";
import { logger } from "../utils/logger";
import { body, param, validationResult } from "express-validator";

const router: express.Router = express.Router();

// Apply auth middleware to all routes
router.use(authenticate);

/**
 * GET /api/personas
 * Get all personas for the user in their organization
 */
router.get("/", async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { organizationId } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required as a query parameter",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    // Ensure default personas exist
    await personaService.ensureDefaultPersonas(organizationId as string);

    const personas = await personaService.getPersonas(
      userId,
      organizationId as string
    );

    // Transform personas to frontend format
    const formattedPersonas = personas.map((persona) => ({
      id: persona.id,
      title: persona.title,
      systemPrompt: persona.systemPrompt,
      isPublic: persona.isPublic,
      isDefault: persona.isDefault,
      isOwned: persona.userId === userId,
      createdAt: persona.createdAt.toISOString(),
      updatedAt: persona.updatedAt.toISOString(),
      user: persona.user
        ? {
            id: persona.user.id,
            name: persona.user.name,
            email: persona.user.email,
          }
        : null,
    }));

    res.json({
      success: true,
      data: { personas: formattedPersonas },
    });
  } catch (error) {
    logger.error("Error getting personas:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get personas",
    });
  }
});

/**
 * GET /api/personas/:id
 * Get a specific persona by ID
 */
router.get(
  "/:id",
  [param("id").isString().notEmpty().withMessage("Persona ID is required")],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const userId = req.user?.id;
      const { organizationId } = req.query;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required as a query parameter",
        });
      }

      // Verify user has access to this organization
      const userOrg = req.user?.organizations.find(
        (org) => org.id === organizationId
      );
      if (!userOrg) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this organization",
        });
      }

      const persona = await personaService.getPersonaById(
        id,
        userId,
        organizationId as string
      );

      if (!persona) {
        return res.status(404).json({
          success: false,
          message: "Persona not found",
        });
      }

      // Transform persona to frontend format
      const formattedPersona = {
        id: persona.id,
        title: persona.title,
        systemPrompt: persona.systemPrompt,
        isPublic: persona.isPublic,
        isDefault: persona.isDefault,
        isOwned: persona.userId === userId,
        createdAt: persona.createdAt.toISOString(),
        updatedAt: persona.updatedAt.toISOString(),
        user: persona.user
          ? {
              id: persona.user.id,
              name: persona.user.name,
              email: persona.user.email,
            }
          : null,
      };

      res.json({
        success: true,
        data: { persona: formattedPersona },
      });
    } catch (error) {
      logger.error("Error getting persona:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get persona",
      });
    }
  }
);

/**
 * POST /api/personas
 * Create a new persona
 */
router.post(
  "/",
  [
    body("title")
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage("Title must be between 1 and 100 characters"),
    body("systemPrompt")
      .isString()
      .trim()
      .isLength({ min: 1, max: 5000 })
      .withMessage("System prompt must be between 1 and 5000 characters"),
    body("isPublic")
      .optional()
      .isBoolean()
      .withMessage("isPublic must be a boolean"),
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const { title, systemPrompt, isPublic, organizationId } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required in request body",
        });
      }

      // Verify user has access to this organization
      const userOrg = req.user?.organizations.find(
        (org) => org.id === organizationId
      );
      if (!userOrg) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this organization",
        });
      }

      const persona = await personaService.createPersona(
        userId,
        organizationId,
        {
          title,
          systemPrompt,
          isPublic,
        }
      );

      // Transform persona to frontend format
      const formattedPersona = {
        id: persona.id,
        title: persona.title,
        systemPrompt: persona.systemPrompt,
        isPublic: persona.isPublic,
        isDefault: persona.isDefault,
        isOwned: true,
        createdAt: persona.createdAt.toISOString(),
        updatedAt: persona.updatedAt.toISOString(),
        user: persona.user
          ? {
              id: persona.user.id,
              name: persona.user.name,
              email: persona.user.email,
            }
          : null,
      };

      res.status(201).json({
        success: true,
        data: { persona: formattedPersona },
      });
    } catch (error) {
      logger.error("Error creating persona:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create persona",
      });
    }
  }
);

/**
 * PUT /api/personas/:id
 * Update a persona
 */
router.put(
  "/:id",
  [
    param("id").isString().notEmpty().withMessage("Persona ID is required"),
    body("title")
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage("Title must be between 1 and 100 characters"),
    body("systemPrompt")
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 5000 })
      .withMessage("System prompt must be between 1 and 5000 characters"),
    body("isPublic")
      .optional()
      .isBoolean()
      .withMessage("isPublic must be a boolean"),
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { title, systemPrompt, isPublic } = req.body;
      const userId = req.user?.id;
      const organizationId = req.user?.organizations[0]?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required",
        });
      }

      const persona = await personaService.updatePersona(
        id,
        userId,
        organizationId,
        {
          title,
          systemPrompt,
          isPublic,
        }
      );

      // Transform persona to frontend format
      const formattedPersona = {
        id: persona.id,
        title: persona.title,
        systemPrompt: persona.systemPrompt,
        isPublic: persona.isPublic,
        isDefault: persona.isDefault,
        isOwned: true,
        createdAt: persona.createdAt.toISOString(),
        updatedAt: persona.updatedAt.toISOString(),
        user: persona.user
          ? {
              id: persona.user.id,
              name: persona.user.name,
              email: persona.user.email,
            }
          : null,
      };

      res.json({
        success: true,
        data: { persona: formattedPersona },
      });
    } catch (error) {
      logger.error("Error updating persona:", error);
      if (
        error instanceof Error &&
        error.message === "Persona not found or not editable"
      ) {
        res.status(404).json({
          success: false,
          message: "Persona not found or not editable",
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to update persona",
        });
      }
    }
  }
);

/**
 * DELETE /api/personas/:id
 * Delete a persona
 */
router.delete(
  "/:id",
  [param("id").isString().notEmpty().withMessage("Persona ID is required")],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const userId = req.user?.id;
      const organizationId = req.user?.organizations[0]?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required",
        });
      }

      await personaService.deletePersona(id, userId, organizationId);

      res.json({
        success: true,
        message: "Persona deleted successfully",
      });
    } catch (error) {
      logger.error("Error deleting persona:", error);
      if (
        error instanceof Error &&
        error.message === "Persona not found or not deletable"
      ) {
        res.status(404).json({
          success: false,
          message: "Persona not found or not deletable",
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to delete persona",
        });
      }
    }
  }
);

export default router;
