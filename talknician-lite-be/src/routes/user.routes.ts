import { Router, Request, Response } from "express";
import { body, validationResult } from "express-validator";
import { prisma } from "@/config/database";
import { authenticate } from "@/middleware/auth.middleware";
import { auth0Service } from "@/services/auth0.service";
import logger from "@/utils/logger";

const router: Router = Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * GET /api/user/profile
 * Get current user's profile information
 */
router.get("/profile", async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        auth0Id: true,
        createdAt: true,
        updatedAt: true,
        organizationMemberships: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Format the response
    const profile = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      auth0Id: user.auth0Id,
      firstName: user.name?.split(" ")[0] || "",
      lastName: user.name?.split(" ").slice(1).join(" ") || "",
      memberSince: user.createdAt.toISOString(),
      lastUpdated: user.updatedAt.toISOString(),
      organizations: user.organizationMemberships.map((membership) => ({
        id: membership.organization.id,
        name: membership.organization.name,
        slug: membership.organization.slug,
        role: membership.role,
      })),
    };

    res.json({
      success: true,
      data: { profile },
    });
  } catch (error) {
    logger.error("Error getting user profile:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get user profile",
    });
  }
});

/**
 * PUT /api/user/profile
 * Update current user's profile information
 */
router.put(
  "/profile",
  [
    body("firstName")
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage("First name must be between 1 and 50 characters"),
    body("lastName")
      .optional()
      .isString()
      .trim()
      .isLength({ min: 0, max: 50 })
      .withMessage("Last name must be between 0 and 50 characters"),
    body("name")
      .optional()
      .isString()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage("Name must be between 1 and 100 characters"),
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const userId = req.user?.id;
      const { firstName, lastName, name } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      // Build the update data
      const updateData: any = {};

      if (name !== undefined) {
        updateData.name = name;
      } else if (firstName !== undefined || lastName !== undefined) {
        // If firstName or lastName provided, combine them
        const currentUser = await prisma.user.findUnique({
          where: { id: userId },
          select: { name: true },
        });

        const currentFirstName = currentUser?.name?.split(" ")[0] || "";
        const currentLastName =
          currentUser?.name?.split(" ").slice(1).join(" ") || "";

        const newFirstName =
          firstName !== undefined ? firstName : currentFirstName;
        const newLastName = lastName !== undefined ? lastName : currentLastName;

        updateData.name = `${newFirstName}${
          newLastName ? ` ${newLastName}` : ""
        }`.trim();
      }

      // Update user in database
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          avatar: true,
          auth0Id: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Try to update Auth0 profile as well (best effort)
      try {
        const authHeader = req.headers.authorization;
        if (authHeader && updateData.name) {
          const token = authHeader.substring(7); // Remove "Bearer " prefix
          await auth0Service.updateUserProfile(updatedUser.auth0Id, {
            name: updateData.name,
          });
        }
      } catch (auth0Error) {
        logger.warn("Failed to update Auth0 profile", {
          error: auth0Error,
          userId,
        });
        // Don't fail the request if Auth0 update fails
      }

      // Format the response
      const profile = {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        avatar: updatedUser.avatar,
        auth0Id: updatedUser.auth0Id,
        firstName: updatedUser.name?.split(" ")[0] || "",
        lastName: updatedUser.name?.split(" ").slice(1).join(" ") || "",
        memberSince: updatedUser.createdAt.toISOString(),
        lastUpdated: updatedUser.updatedAt.toISOString(),
      };

      logger.info("User profile updated successfully", {
        userId,
        updatedFields: Object.keys(updateData),
      });

      res.json({
        success: true,
        message: "Profile updated successfully",
        data: { profile },
      });
    } catch (error) {
      logger.error("Error updating user profile:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update user profile",
      });
    }
  }
);

/**
 * POST /api/user/change-password
 * Change user's password
 */
router.post(
  "/change-password",
  [
    body("currentPassword")
      .isString()
      .notEmpty()
      .withMessage("Current password is required"),
    body("newPassword")
      .isString()
      .isLength({ min: 8 })
      .withMessage("New password must be at least 8 characters long")
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage(
        "New password must contain at least one lowercase letter, one uppercase letter, and one number"
      ),
    body("confirmPassword")
      .isString()
      .notEmpty()
      .withMessage("Password confirmation is required")
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error("Password confirmation does not match new password");
        }
        return true;
      }),
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: errors.array(),
        });
      }

      const userId = req.user?.id;
      const auth0Id = req.user?.auth0Id;
      const { currentPassword, newPassword } = req.body;

      if (!userId || !auth0Id) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      // Verify current password by attempting login with Auth0
      try {
        await auth0Service.login(req.user!.email, currentPassword);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: "Current password is incorrect",
        });
      }

      // Change password in Auth0
      try {
        await auth0Service.changePassword(auth0Id, newPassword);
      } catch (error) {
        logger.error("Failed to change password in Auth0", {
          error,
          userId,
          auth0Id,
        });
        return res.status(500).json({
          success: false,
          message: "Failed to change password. Please try again.",
        });
      }

      logger.info("Password changed successfully", { userId });

      res.json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      logger.error("Error changing password:", error);
      res.status(500).json({
        success: false,
        message: "Failed to change password",
      });
    }
  }
);

/**
 * POST /api/user/upload-avatar
 * Upload user avatar (placeholder for future implementation)
 */
router.post("/upload-avatar", async (req: Request, res: Response) => {
  // This is a placeholder endpoint for avatar upload functionality
  // In a full implementation, you would integrate with a file upload service
  res.status(501).json({
    success: false,
    message: "Avatar upload not implemented yet",
  });
});

export default router;
