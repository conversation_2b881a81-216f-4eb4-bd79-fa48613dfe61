import express, { Request, Response, NextFunction, Router } from "express";
import * as MessageDocumentService from "../services/message-document.service";
import { authenticate } from "../middleware/auth.middleware";
import { Prisma } from "@prisma/client";
import { azureStorageService } from "@/services/azure-storage.service";
import path from "path";
import { logger } from "@/utils/logger";
import multer from "multer";
import { openai, uploadFileFromAzureToOpenAI } from "@/services/openai.service";

const router: Router = express.Router();

// Configure multer for file uploads (using memory storage for Azure upload)
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory for direct Azure upload
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common document types
    const allowedTypes = [
      ".pdf",
      ".docx",
      ".doc",
      ".txt",
      ".md",
      ".csv",
      ".xlsx",
      ".xls",
      ".pptx",
      ".ppt",
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".tiff",
      ".webp",
    ];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error("File type not supported"));
    }
  },
});

router.post(
  "/upload",
  authenticate,
  (req: Request, res: Response, next: NextFunction) => {
    logger.info("Upload route hit", {
      contentType: req.get("Content-Type"),
      method: req.method,
      body: req.body,
      headers: req.headers,
    });
    next();
  },
  upload.array("files", 10),
  async (req: Request, res: Response) => {
    try {
      // Get organization ID from request body or query
      const organizationId =
        req.body.organizationId || req.query.organizationId;
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No files uploaded",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required in request body or query",
        });
      }

      // Verify user has access to this organization
      const user = (req as any).user;
      const userOrg = user?.organizations.find(
        (org: { id: any }) => org.id === organizationId
      );
      if (!userOrg) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this organization",
        });
      }

      const uploadedDocuments = [];

      for (const file of files) {
        // Upload to Azure Blob Storage from buffer
        const azureBlobUrl = await azureStorageService.uploadFileFromBuffer(
          file.buffer,
          file.originalname,
          file.mimetype,
          organizationId
        );

        // Upload file to OpenAI
        const openaiFile = await uploadFileFromAzureToOpenAI(
          azureBlobUrl,
          file.originalname
        );

        // Create a message document record
        const newDocument = await MessageDocumentService.createMessageDocument({
          filename: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          azureBlobUrl,
          userId: user.id,
          openaiFileId: openaiFile.id,
          messageId: null,
        });

        uploadedDocuments.push(newDocument);
      }
      res.status(201).json({
        success: true,
        message: "Documents uploaded successfully",
        data: uploadedDocuments,
      });
    } catch (error) {
      logger.error("Error uploading document:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload document",
      });
    }
  }
);

// Get MessageDocuments by Message ID
router.get(
  "/message/:messageId",
  authenticate,
  async (req: Request, res: Response) => {
    try {
      const user = (req as any).user;
      const { messageId } = req.params;

      const messageDocuments =
        await MessageDocumentService.getMessageDocumentsByMessageId(messageId);
      res.json(messageDocuments);
    } catch (error: any) {
      res.status(500).json({ error: "An unexpected error occurred." });
    }
  }
);

// Middleware to check document ownership and org membership
const documentAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = (req as any).user;
    const { id } = req.params;

    const messageDocument = await MessageDocumentService.getMessageDocumentById(
      id
    );
    if (!messageDocument) {
      return res.status(404).json({ message: "MessageDocument not found" });
    }

    (req as any).messageDocument = messageDocument;
    next();
  } catch (error: any) {
    res.status(500).json({ error: "An unexpected error occurred." });
  }
};

// Get MessageDocument by ID
router.get(
  "/:id",
  authenticate,
  documentAuthMiddleware,
  async (req: Request, res: Response) => {
    res.json((req as any).messageDocument);
  }
);

// Update MessageDocument
router.put(
  "/:id",
  authenticate,
  documentAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const messageDocument =
        await MessageDocumentService.updateMessageDocument(
          req.params.id,
          req.body
        );
      res.json(messageDocument);
    } catch (error: any) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        return res.status(400).json({ error: error.message });
      }
      res.status(500).json({ error: "An unexpected error occurred." });
    }
  }
);

// Delete MessageDocument
router.delete(
  "/:id",
  authenticate,
  documentAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const messageDocument =
        await MessageDocumentService.getMessageDocumentById(req.params.id);

      if (!messageDocument) {
        return res.status(404).json({ message: "MessageDocument not found" });
      }

      // Delete from Azure and OpenAI first
      if (messageDocument.azureBlobUrl) {
        await azureStorageService.deleteFile(messageDocument.azureBlobUrl);
      }
      if (messageDocument.openaiFileId) {
        await openai.files.del(messageDocument.openaiFileId);
      }

      // Then delete from database
      await MessageDocumentService.deleteMessageDocument(req.params.id);
      res.status(204).send();
    } catch (error: any) {
      res.status(500).json({ error: "An unexpected error occurred." });
    }
  }
);

export default router;
