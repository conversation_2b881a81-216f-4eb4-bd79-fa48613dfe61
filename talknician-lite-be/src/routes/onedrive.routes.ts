import express from "express";
import axios from "axios";
import fs from "fs";
import path from "path";
import { prisma } from "../config/database";
import { toFile } from "openai";
import { authenticate } from "../middleware/auth.middleware";
import { azureStorageService } from "../services/azure-storage.service";
import logger from "@/utils/logger";
import { triggerPusherEvent } from "../services/pusher.service";
import { openai } from "@/services/openai.service";

const router: express.Router = express.Router();

const CLIENT_ID = process.env.ONEDRIVE_CLIENT_ID;
const CLIENT_SECRET = process.env.ONEDRIVE_CLIENT_SECRET;
const REDIRECT_URI =
  process.env.ONEDRIVE_REDIRECT_URI ||
  "http://localhost:8000/api/integrations/onedrive/callback";
const AUTHORITY = "https://login.microsoftonline.com/common/oauth2/v2.0";
const SCOPES = [
  "openid",
  "profile",
  "offline_access",
  "User.Read",
  "Files.ReadWrite.All",
];

// Background processing function for OneDrive files
async function processOneDriveFileInBackground(
  documentId: string,
  fileId: string,
  fileName: string,
  accessToken: string,
  organizationId: string,
  userId: string
) {
  try {
    logger.info("Background processing started", {
      documentId,
      fileId,
      fileName,
    });

    // 1. Fetch file from OneDrive
    const fileRes = await axios.get(
      `https://graph.microsoft.com/v1.0/me/drive/items/${fileId}/content`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
        responseType: "arraybuffer",
      }
    );

    const fileBuffer = Buffer.from(fileRes.data);

    // 2. Create temporary file for Azure upload
    const tempDir = path.join(process.cwd(), "temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilename = `${Date.now()}-${Math.random()
      .toString(36)
      .substring(7)}-${fileName}`;
    const tempPath = path.join(tempDir, tempFilename);

    try {
      // Write to temp file
      fs.writeFileSync(tempPath, fileBuffer);

      // Create multer-like file object for Azure upload
      const tempFile = {
        path: tempPath,
        originalname: fileName,
        mimetype: "application/octet-stream",
        size: fileBuffer.length,
      } as Express.Multer.File;

      // 3. Upload to Azure Storage
      const azureBlobUrl = await azureStorageService.uploadFile(
        tempFile,
        organizationId
      );

      logger.info("Background: File uploaded to Azure Storage", {
        documentId,
        fileName,
        azureBlobUrl,
      });

      // 4. Update document with Azure URL and file details
      const document = await prisma.document.update({
        where: { id: documentId },
        data: {
          filename: tempFilename,
          size: fileBuffer.length,
          azureBlobUrl,
          status: "COMPLETED", // Mark as completed after Azure upload
          updatedAt: new Date(),
        },
      });

      // 5. Add to RAG (OpenAI processing)
      await addDocumentToRAG(document, organizationId);

      triggerPusherEvent(
        `private-org-${organizationId}`,
        "file-status-update",
        {
          documentId: document.id,
          status: "COMPLETED",
          inRAG: true,
        }
      );

      logger.info("Background processing completed successfully", {
        documentId,
        fileName,
      });
    } finally {
      // Clean up temp file
      if (fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }
    }
  } catch (error) {
    logger.error("Background processing failed", {
      documentId,
      fileName,
      error: error instanceof Error ? error.message : String(error),
    });

    // Update document status to FAILED
    try {
      await prisma.document.update({
        where: { id: documentId },
        data: {
          status: "FAILED",
          updatedAt: new Date(),
        },
      });
      triggerPusherEvent(
        `private-org-${organizationId}`,
        "file-status-update",
        {
          documentId: documentId,
          status: "FAILED",
        }
      );
    } catch (dbError) {
      logger.error("Failed to update document status to FAILED", {
        documentId,
        dbError: dbError instanceof Error ? dbError.message : String(dbError),
      });
    }
  }
}

// Helper function to add document to RAG
async function addDocumentToRAG(document: any, organizationId: string) {
  try {
    // Get or create organization-specific vector store
    const vectorStoreId = await getOrCreateOrgVectorStore(organizationId);

    // Get file stream from Azure Storage
    const fileStream = await azureStorageService.getFileStream(
      document.azureBlobUrl
    );

    // Upload file to OpenAI
    const openaiFile = await openai.files.create({
      file: await toFile(fileStream, document.filename),
      purpose: "assistants",
    });

    // Add file to organization's vector store
    await openai.vectorStores.files.create(vectorStoreId, {
      file_id: openaiFile.id,
    });

    // Update document with OpenAI file ID
    await prisma.document.update({
      where: { id: document.id },
      data: {
        openaiFileId: openaiFile.id,
        status: "COMPLETED",
      },
    });

    logger.info("Document added to RAG successfully", {
      documentId: document.id,
      openaiFileId: openaiFile.id,
    });
  } catch (error) {
    logger.error("Failed to add document to RAG", {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

// Helper function to get or create organization vector store
async function getOrCreateOrgVectorStore(
  organizationId: string
): Promise<string> {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
    select: { vectorStoreId: true, name: true },
  });

  if (!organization) throw new Error("Organization not found");
  if (organization.vectorStoreId) return organization.vectorStoreId;

  const vectorStore = await openai.vectorStores.create({
    name: `${organization.name} Knowledge Base`,
    expires_after: { anchor: "last_active_at", days: 365 },
  });

  await prisma.organization.update({
    where: { id: organizationId },
    data: { vectorStoreId: vectorStore.id },
  });

  return vectorStore.id;
}

// 1. Start OAuth flow
router.get("/auth", (req, res) => {
  const url =
    `${AUTHORITY}/authorize?client_id=${CLIENT_ID}` +
    `&response_type=code` +
    `&redirect_uri=${encodeURIComponent(REDIRECT_URI)}` +
    `&response_mode=query` +
    `&scope=${encodeURIComponent(SCOPES.join(" "))}` +
    `&state=12345`;
  res.redirect(url);
});

// 2. OAuth callback
router.get("/callback", async (req, res) => {
  const code = req.query.code as string;
  if (!code) return res.status(400).send("Missing code");
  try {
    const tokenRes = await axios.post(
      `${AUTHORITY}/token`,
      new URLSearchParams({
        client_id: CLIENT_ID!,
        client_secret: CLIENT_SECRET!,
        code,
        redirect_uri: REDIRECT_URI,
        grant_type: "authorization_code",
      }),
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );
    // Store access token in session
    (req.session as any).onedrive = {
      access_token: tokenRes.data.access_token,
      refresh_token: tokenRes.data.refresh_token,
      expires_at: Date.now() + tokenRes.data.expires_in * 1000,
    };
    // Instead of redirect, close popup and notify opener
    res.redirect(`${process.env.FRONTEND_URL}/integrations/callback`);
  } catch (err) {
    res.status(500).send("OAuth error");
  }
});

// 3. Check connection status
router.get("/status", (req, res) => {
  const token = (req.session as any).onedrive?.access_token;
  const expires_at = (req.session as any).onedrive?.expires_at;

  if (!token || (expires_at && Date.now() > expires_at)) {
    return res.json({ success: true, connected: false });
  }

  res.json({ success: true, connected: true });
});

// 4. Disconnect OneDrive
router.post("/disconnect", (req, res) => {
  if ((req.session as any).onedrive) {
    delete (req.session as any).onedrive;
  }
  res.json({ success: true, message: "OneDrive disconnected" });
});

// 4. List files/folders
router.get("/list", authenticate, async (req, res) => {
  const pathParam = typeof req.query.path === "string" ? req.query.path : "/";
  const token = (req.session as any).onedrive?.access_token;
  const organizationId = req.user?.organizations[0].id;

  if (!token) {
    return res.status(401).json({ error: "Not authenticated with OneDrive" });
  }
  if (!organizationId || typeof organizationId !== "string") {
    return res.status(400).json({ error: "Organization ID is required" });
  }

  try {
    let url;
    if (pathParam === "/") {
      url = "https://graph.microsoft.com/v1.0/me/drive/root/children";
    } else if (typeof pathParam === "string" && !pathParam.startsWith("/")) {
      url = `https://graph.microsoft.com/v1.0/me/drive/items/${encodeURIComponent(
        pathParam
      )}/children`;
    } else {
      url = `https://graph.microsoft.com/v1.0/me/drive/root:${encodeURI(
        pathParam
      )}:/children`;
    }

    const graphRes = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });

    const oneDriveFiles = graphRes.data.value;

    if (!oneDriveFiles || oneDriveFiles.length === 0) {
      return res.json({ success: true, data: [] });
    }

    // Get file names to check against our database
    const fileNames = oneDriveFiles
      .filter((file: any) => !file.folder)
      .map((file: any) => file.name);

    // Find existing documents in our database
    if (fileNames.length > 0) {
      const existingDocuments = await prisma.document.findMany({
        where: {
          organizationId,
          originalName: {
            in: fileNames,
          },
        },
        select: {
          originalName: true,
          status: true,
          openaiFileId: true,
          isDeleted: false,
        },
      });
      console.log("existingDocuments", existingDocuments);

      // Create a map for quick lookup
      const docStatusMap = new Map();
      for (const doc of existingDocuments) {
        docStatusMap.set(doc.originalName, {
          status: doc.status,
          inRAG: !!doc.openaiFileId && doc.status === "COMPLETED",
        });
      }

      // Augment the OneDrive file list with our RAG status
      const augmentedFiles = oneDriveFiles.map((file: any) => {
        if (file.folder) {
          return file;
        }
        const ragStatus = docStatusMap.get(file.name);
        return {
          ...file,
          ragStatus: ragStatus || null,
        };
      });

      return res.json({ success: true, data: augmentedFiles });
    }

    res.json({ success: true, data: oneDriveFiles });
  } catch (err) {
    logger.error("Failed to list files", { error: err });
    res.status(500).json({ error: "Failed to list files" });
  }
});

// Add-to-RAG endpoint for OneDrive files
router.post("/add-to-rag", authenticate, async (req, res) => {
  const { fileId, fileName } = req.body;
  let organizationId = req.body.organizationId;
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({ error: "Not authenticated" });
  }
  if (!organizationId || typeof organizationId !== "string")
    throw new Error("organizationId is required and must be a string");
  organizationId = organizationId as string;

  // Verify user has access to this organization
  const userOrg = req.user?.organizations.find(
    (org) => org.id === organizationId
  );
  if (!userOrg) {
    return res.status(403).json({
      error: "Access denied to this organization",
    });
  }

  try {
    // 1. Check if already imported
    let document = await prisma.document.findFirst({
      where: { originalName: fileName, organizationId },
    });

    logger.info("OneDrive add-to-rag: Document lookup", {
      fileName,
      organizationId,
      documentFound: !!document,
      documentId: document?.id,
      azureBlobUrl: document?.azureBlobUrl,
      localPath: document?.localPath,
      status: document?.status,
    });

    // If document already exists and is completed, return it
    if (document && document.openaiFileId && document.status === "COMPLETED") {
      return res.json({
        success: true,
        data: {
          id: document.id,
          filename: document.filename,
          originalName: document.originalName,
          size: document.size,
          status: document.status,
          organizationId: document.organizationId,
          createdAt: document.createdAt.toISOString(),
          openaiFileId: document.openaiFileId,
          inRAG: true,
          oneDriveFileId: document.oneDriveFileId,
        },
      });
    }

    // If document is already processing, return processing status
    if (document && document.status === "PROCESSING") {
      return res.json({
        success: true,
        data: {
          id: document.id,
          filename: document.filename,
          originalName: document.originalName,
          size: document.size || 0,
          status: "PROCESSING",
          organizationId: document.organizationId,
          createdAt: document.createdAt.toISOString(),
          openaiFileId: null,
          inRAG: false,
        },
      });
    }

    // Check if document exists but file is missing from storage
    const needsReUpload =
      document &&
      !document.azureBlobUrl &&
      (!document.localPath || !fs.existsSync(document.localPath));

    // Create or update document with PROCESSING status immediately
    if (!document || needsReUpload) {
      // Check OneDrive authentication first
      const accessToken = (req.session as any).onedrive?.access_token;
      if (!accessToken) {
        return res
          .status(401)
          .json({ success: false, message: "Not authenticated with OneDrive" });
      }

      // Create document with PROCESSING status immediately
      if (!document) {
        document = await prisma.document.create({
          data: {
            filename: `processing-${Date.now()}-${fileName}`,
            originalName: fileName,
            size: 0, // Will be updated when processing completes
            mimeType: "application/octet-stream",
            localPath: null,
            azureBlobUrl: null,
            organizationId,
            userId,
            uploadedBy: userId,
            status: "PROCESSING",
          },
        });
        logger.info(
          "OneDrive add-to-rag: Created document with PROCESSING status",
          {
            documentId: document.id,
            fileName,
          }
        );
      } else if (needsReUpload) {
        // Update existing document to PROCESSING status
        document = await prisma.document.update({
          where: { id: document.id },
          data: {
            status: "PROCESSING",
            updatedAt: new Date(),
          },
        });
        logger.info(
          "OneDrive add-to-rag: Updated document to PROCESSING status",
          {
            documentId: document.id,
            fileName,
          }
        );
      }

      // Return immediately with PROCESSING status
      const processingResponse = {
        success: true,
        data: {
          id: document.id,
          filename: document.filename,
          originalName: document.originalName,
          size: document.size,
          status: "PROCESSING",
          organizationId: document.organizationId,
          createdAt: document.createdAt.toISOString(),
          openaiFileId: null,
          inRAG: false,
        },
      };

      // Start background processing (don't await)
      processOneDriveFileInBackground(
        document.id,
        fileId,
        fileName,
        accessToken,
        organizationId,
        userId
      );

      return res.json(processingResponse);
    }

    // This should not happen - document exists but doesn't need re-upload and isn't completed
    // This could be a document that's already in RAG or has some other status
    res.json({
      success: true,
      data: {
        id: document.id,
        filename: document.filename,
        originalName: document.originalName,
        size: document.size,
        status: document.status,
        organizationId: document.organizationId,
        createdAt: document.createdAt.toISOString(),
        openaiFileId: document.openaiFileId,
        inRAG: !!document.openaiFileId,
      },
    });
  } catch (error) {
    console.error("Error in OneDrive add-to-rag:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to add OneDrive file to RAG" });
  }
});

export default router;
