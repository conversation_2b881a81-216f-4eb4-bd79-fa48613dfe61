import { Router, Request, Response, NextFunction } from "express";
import { body, validationResult } from "express-validator";
import { auth0Service } from "@/services/auth0.service";
import { prisma } from "@/config/database";
import { authenticate } from "@/middleware/auth.middleware";
import { logger } from "@/utils/logger";

const router: Router = Router();

// Types for Auth0 user
interface Auth0User {
  user_id: string;
  email: string;
  name?: string;
  picture?: string;
}

// Types for organization membership
interface OrganizationMembership {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  role: string;
}

// Validation rules
const loginValidation = [
  body("email")
    .custom((value) => {
      // For testing, allow more flexible email formats
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        throw new Error("Invalid email format");
      }
      return true;
    })
    .normalizeEmail(),
  body("password").isLength({ min: 6 }),
];

const signupValidation = [
  body("email")
    .custom((value) => {
      // For testing, allow more flexible email formats
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        throw new Error("Invalid email format");
      }
      return true;
    })
    .normalizeEmail(),
  body("password")
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body("name").optional().isLength({ min: 2, max: 50 }),
];

const organizationValidation = [
  body("name").isLength({ min: 2, max: 50 }),
  body("slug").optional().isSlug().isLength({ min: 2, max: 30 }),
];

/**
 * Helper function to find or create user with email-first account linking
 * This ensures users with the same email but different auth providers get linked
 */
async function findOrCreateUserWithAccountLinking(auth0User: Auth0User) {
  logger.info("🔍 findOrCreateUserWithAccountLinking started", {
    auth0User,
    user_id: auth0User.user_id,
    email: auth0User.email,
  });

  const includeOptions = {
    include: {
      organizationMemberships: {
        include: {
          organization: true,
        },
      },
    },
  };

  // First, try to find user by Auth0 ID (existing behavior)
  logger.info("🔎 Step 1: Looking for user by Auth0 ID", {
    auth0Id: auth0User.user_id,
  });

  let user = await prisma.user.findUnique({
    where: { auth0Id: auth0User.user_id },
    ...includeOptions,
  });

  if (user) {
    logger.info("✅ Step 1: User found by Auth0 ID", {
      userId: user.id,
      email: user.email,
      auth0Id: user.auth0Id,
    });

    // User found by Auth0 ID - update profile info and return
    user = await prisma.user.update({
      where: { id: user.id },
      data: {
        name: auth0User.name,
        avatar: auth0User.picture,
      },
      ...includeOptions,
    });

    logger.info("✅ User profile updated", {
      userId: user.id,
      email: user.email,
      auth0Id: auth0User.user_id,
    });

    return user;
  }

  // User not found by Auth0 ID, check by email for account linking
  logger.info("🔎 Step 2: User not found by Auth0 ID, checking by email", {
    email: auth0User.email,
  });

  user = await prisma.user.findUnique({
    where: { email: auth0User.email },
    ...includeOptions,
  });

  if (user) {
    logger.info("✅ Step 2: User found by email - linking accounts", {
      userId: user.id,
      email: user.email,
      existingAuth0Id: user.auth0Id,
      newAuth0Id: auth0User.user_id,
    });

    // User exists with same email but different auth provider - link accounts
    const previousAuth0Id = user.auth0Id;

    user = await prisma.user.update({
      where: { id: user.id },
      data: {
        auth0Id: auth0User.user_id, // Link the new auth provider
        name: auth0User.name,
        avatar: auth0User.picture,
      },
      ...includeOptions,
    });

    logger.info("🔗 ACCOUNT LINKED - user found by email", {
      userId: user.id,
      email: user.email,
      previousAuth0Id,
      newAuth0Id: auth0User.user_id,
      message:
        "Successfully linked existing email account with new auth provider",
      accountLinking: true,
    });

    return user;
  }

  // User doesn't exist - create new user
  logger.info("🆕 Step 3: User not found by email either - creating new user", {
    auth0Id: auth0User.user_id,
    email: auth0User.email,
    name: auth0User.name,
  });

  user = await prisma.user.create({
    data: {
      auth0Id: auth0User.user_id,
      email: auth0User.email,
      name: auth0User.name,
      avatar: auth0User.picture,
    },
    ...includeOptions,
  });

  logger.info("✅ New user created successfully", {
    userId: user.id,
    email: user.email,
    auth0Id: auth0User.user_id,
  });

  return user;
}

/**
 * POST /api/auth/login
 * Custom login using Auth0 Resource Owner Password Grant
 */
router.post("/login", loginValidation, async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation Error",
        details: errors.array(),
      });
    }

    const { email, password } = req.body;

    // Authenticate with Auth0
    const auth0Response = await auth0Service.login(email, password);

    // Get user profile from Auth0
    const auth0User = await auth0Service.getUserProfile(
      auth0Response.access_token
    );

    // Find or create user with email-first account linking
    const user = await findOrCreateUserWithAccountLinking(auth0User);

    // Check if user needs to create/join organization
    const needsOrganization = user.organizationMemberships.length === 0;

    logger.info("User login successful", {
      userId: user.id,
      email: user.email,
      needsOrganization,
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          organizations: user.organizationMemberships.map(
            (membership: OrganizationMembership) => ({
              id: membership.organization.id,
              name: membership.organization.name,
              slug: membership.organization.slug,
              role: membership.role,
            })
          ),
        },
        tokens: {
          accessToken: auth0Response.access_token,
          tokenType: auth0Response.token_type,
          expiresIn: auth0Response.expires_in,
        },
        needsOrganization,
      },
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Login failed", { error: errorMessage });
    res.status(401).json({
      error: "Authentication Failed",
      message: errorMessage,
    });
  }
});

/**
 * POST /api/auth/signup
 * Create new user account
 */
router.post(
  "/signup",
  signupValidation,
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { email, password, name, invitationId } = req.body;

      // If invitationId is provided, validate it first
      if (invitationId) {
        const invitation = await prisma.organizationInvitation.findUnique({
          where: { id: invitationId },
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        });

        if (!invitation) {
          return res.status(400).json({
            error: "Invalid Invitation",
            message: "The invitation link is invalid or has expired.",
          });
        }

        if (invitation.expiresAt < new Date()) {
          return res.status(400).json({
            error: "Expired Invitation",
            message: "This invitation has expired.",
          });
        }

        if (invitation.usedAt) {
          return res.status(400).json({
            error: "Invitation Already Used",
            message: "This invitation has already been used.",
          });
        }

        if (invitation.email !== email) {
          return res.status(400).json({
            error: "Email Mismatch",
            message: "The email address doesn't match the invitation.",
          });
        }
      }

      // Check if user already exists with this email
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(409).json({
          error: "Account Already Exists",
          message:
            "An account with this email already exists. Please try logging in instead.",
        });
      }

      // Create user in Auth0
      const auth0User = await auth0Service.signupUser({
        email,
        password,
        name,
      });

      // Create user in our database
      const user = await prisma.user.create({
        data: {
          auth0Id: auth0User.user_id,
          email: auth0User.email,
          name: auth0User.name,
          avatar: auth0User.picture,
        },
      });

      // Process invitations
      const processedInvitations = [];
      let invitationsToProcess: Array<{
        id: string;
        email: string;
        role: any;
        organizationId: string;
        organization: {
          id: string;
          name: string;
          slug: string;
        };
      }> = [];

      if (invitationId) {
        // Process specific invitation
        const specificInvitation =
          await prisma.organizationInvitation.findUnique({
            where: { id: invitationId },
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          });

        if (specificInvitation) {
          invitationsToProcess = [specificInvitation];
        }
      } else {
        // Process all pending invitations for this email
        invitationsToProcess = await prisma.organizationInvitation.findMany({
          where: {
            email: auth0User.email,
            usedAt: null,
            expiresAt: {
              gt: new Date(),
            },
          },
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        });
      }

      // Auto-accept all valid invitations
      for (const invitation of invitationsToProcess) {
        await prisma.organizationMember.create({
          data: {
            userId: user.id,
            organizationId: invitation.organizationId,
            role: invitation.role,
            status: "ACTIVE",
          },
        });

        processedInvitations.push({
          organizationName: invitation.organization.name,
          role: invitation.role,
        });

        // Mark invitation as used
        await prisma.organizationInvitation.update({
          where: { id: invitation.id },
          data: { usedAt: new Date() },
        });

        logger.info("Auto-joined organization during signup", {
          userId: user.id,
          organizationId: invitation.organizationId,
          invitationId: invitation.id,
        });
      }

      // Send custom verification email (instead of Auth0's default)
      try {
        const { emailService } = await import("../services/email.service");
        const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${auth0User.user_id}`;
        await emailService.sendVerificationEmail(user.email, verificationLink);
      } catch (emailError) {
        logger.warn("Failed to send verification email", { error: emailError });
        // Fallback to Auth0's verification email if our service fails
        try {
          await auth0Service.sendVerificationEmail(auth0User.user_id);
          logger.info("Sent Auth0 verification email as fallback");
        } catch (fallbackError) {
          logger.error("Both custom and Auth0 verification emails failed", {
            customError: emailError,
            fallbackError,
          });
        }
      }

      logger.info("User signup successful", {
        userId: user.id,
        email: user.email,
        autoJoinedOrganizations: processedInvitations.length,
        invitationId: invitationId || null,
      });

      const needsOrganization = processedInvitations.length === 0;

      let message =
        "Account created successfully. Please check your email for verification.";
      if (processedInvitations.length > 0) {
        const orgNames = processedInvitations
          .map((inv) => inv.organizationName)
          .join(", ");
        message += ` You've been automatically added to: ${orgNames}.`;
      }

      res.status(201).json({
        success: true,
        message,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
          },
          needsOrganization,
          autoJoinedOrganizations: processedInvitations,
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Signup failed", {
        email: req.body.email,
        error: errorMessage,
      });

      const statusCode = errorMessage.includes("already exists") ? 409 : 400;
      res.status(statusCode).json({
        error: "Signup Failed",
        message: errorMessage,
      });
    }
  }
);

/**
 * GET /api/auth/me
 * Get current user profile with organizations
 */
router.get(
  "/me",
  authenticate,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      res.json({
        success: true,
        data: {
          user: req.user,
        },
      });
    } catch (error: unknown) {
      next(error);
    }
  }
);

/**
 * POST /api/auth/create-organization
 * Create new organization for user
 */
router.post(
  "/create-organization",
  authenticate,
  organizationValidation,
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { name, slug: providedSlug } = req.body;
      const userId = req.user!.id;

      // Generate slug if not provided
      const slug =
        providedSlug ||
        name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .substring(0, 30);

      // Check if slug is available
      const existingOrg = await prisma.organization.findUnique({
        where: { slug },
      });

      if (existingOrg) {
        return res.status(409).json({
          error: "Slug Unavailable",
          message: "Organization slug already exists",
        });
      }

      // Create organization
      const organization = await prisma.organization.create({
        data: {
          name,
          slug,
          members: {
            create: {
              userId,
              role: "OWNER",
            },
          },
        },
        include: {
          members: {
            include: {
              user: {
                select: { id: true, name: true, email: true, avatar: true },
              },
            },
          },
        },
      });

      logger.info("Organization created", {
        organizationId: organization.id,
        userId,
        name: organization.name,
      });

      res.status(201).json({
        success: true,
        message: "Organization created successfully",
        data: {
          organization: {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            role: "OWNER",
            plan: organization.plan,
            createdAt: organization.createdAt,
          },
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Organization creation failed", {
        userId: req.user?.id,
        error: errorMessage,
      });

      res.status(500).json({
        error: "Organization Creation Failed",
        message: "Failed to create organization",
      });
    }
  }
);

/**
 * POST /api/auth/join-organization
 * Join existing organization by slug
 */
router.post(
  "/join-organization",
  authenticate,
  body("slug").isSlug(),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { slug } = req.body;
      const userId = req.user!.id;

      // Find organization
      const organization = await prisma.organization.findUnique({
        where: { slug },
        include: {
          members: {
            where: { userId },
          },
        },
      });

      if (!organization) {
        return res.status(404).json({
          error: "Organization Not Found",
          message: "Organization with this slug does not exist",
        });
      }

      // Check if already a member
      if (organization.members.length > 0) {
        return res.status(409).json({
          error: "Already Member",
          message: "You are already a member of this organization",
        });
      }

      // Add user to organization
      await prisma.organizationMember.create({
        data: {
          userId,
          organizationId: organization.id,
          role: "MEMBER",
        },
      });

      logger.info("User joined organization", {
        organizationId: organization.id,
        userId,
        slug,
      });

      res.json({
        success: true,
        message: "Successfully joined organization",
        data: {
          organization: {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            role: "MEMBER",
            plan: organization.plan,
          },
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Join organization failed", {
        userId: req.user?.id,
        slug: req.body.slug,
        error: errorMessage,
      });

      res.status(500).json({
        error: "Join Failed",
        message: "Failed to join organization",
      });
    }
  }
);

/**
 * POST /api/auth/process-invitations
 * Process pending invitations for the authenticated user
 */
router.post(
  "/process-invitations",
  authenticate,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      const userEmail = req.user!.email;

      // Find pending invitations for this user's email
      const pendingInvitations = await prisma.organizationInvitation.findMany({
        where: {
          email: userEmail,
          usedAt: null,
          expiresAt: {
            gt: new Date(),
          },
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      const processedInvitations = [];

      // Auto-accept all valid invitations
      for (const invitation of pendingInvitations) {
        // Check if user is already a member
        const existingMember = await prisma.organizationMember.findUnique({
          where: {
            userId_organizationId: {
              userId,
              organizationId: invitation.organizationId,
            },
          },
        });

        if (!existingMember) {
          await prisma.organizationMember.create({
            data: {
              userId,
              organizationId: invitation.organizationId,
              role: invitation.role,
              status: "ACTIVE",
            },
          });

          processedInvitations.push({
            organizationName: invitation.organization.name,
            role: invitation.role,
          });
        }

        // Mark invitation as used
        await prisma.organizationInvitation.update({
          where: { id: invitation.id },
          data: { usedAt: new Date() },
        });

        logger.info("Processed invitation", {
          userId,
          organizationId: invitation.organizationId,
          invitationId: invitation.id,
        });
      }

      res.json({
        success: true,
        message: `Processed ${processedInvitations.length} invitation(s)`,
        data: {
          processedInvitations,
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Process invitations failed", {
        userId: req.user?.id,
        error: errorMessage,
      });

      res.status(500).json({
        error: "Process Failed",
        message: "Failed to process invitations",
      });
    }
  }
);

/**
 * POST /api/auth/forgot-password
 * Request password reset
 */
router.post(
  "/forgot-password",
  body("email").isEmail().normalizeEmail(),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { email } = req.body;

      // Always send same response for security (don't reveal if email exists)
      try {
        await auth0Service.resetPassword(email);
        logger.info("Password reset email sent", { email });
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        logger.error("Password reset failed", { email, error: errorMessage });
        // Don't throw - still send success response
      }

      res.json({
        success: true,
        message: "Password reset instructions sent to your email",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Password reset failed", {
        email: req.body.email,
        error: errorMessage,
      });

      // Always send success response for security
      res.json({
        success: true,
        message:
          "If an account exists, password reset instructions have been sent",
      });
    }
  }
);

/**
 * POST /api/auth/verify-email
 * Verify email with token
 */
router.post(
  "/verify-email",
  body("token").notEmpty(),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { token } = req.body;

      // Verify the token and get user info
      // This would typically involve decoding the token or looking it up in database
      // For now, we'll simulate success since Auth0 handles verification

      // In a real implementation, you might:
      // 1. Decode the verification token
      // 2. Find user by token
      // 3. Mark user as verified in database
      // 4. Update Auth0 user verification status

      logger.info("Email verification successful", { token });

      res.json({
        success: true,
        message: "Email verified successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Email verification failed", {
        token: req.body.token,
        error: errorMessage,
      });

      res.status(400).json({
        error: "Verification Failed",
        message: "Invalid or expired verification token",
      });
    }
  }
);

/**
 * POST /api/auth/resend-verification
 * Resend email verification
 */
router.post(
  "/resend-verification",
  body("email").isEmail().normalizeEmail(),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: "Validation Error",
          details: errors.array(),
        });
      }

      const { email } = req.body;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        return res.status(404).json({
          error: "User Not Found",
          message: "No account found with this email address",
        });
      }

      // Resend verification email through Auth0
      await auth0Service.sendVerificationEmail(user.auth0Id);

      logger.info("Verification email resent", {
        userId: user.id,
        email: user.email,
      });

      res.json({
        success: true,
        message: "Verification email sent successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("Resend verification failed", {
        email: req.body.email,
        error: errorMessage,
      });

      res.status(500).json({
        error: "Resend Failed",
        message: "Failed to resend verification email",
      });
    }
  }
);

/**
 * POST /api/auth/social
 * Handle social login (Google, Microsoft) from Auth0
 */
router.post("/social", async (req: Request, res: Response) => {
  try {
    const { access_token } = req.body;

    if (!access_token) {
      return res.status(400).json({
        error: "Validation Error",
        message: "Access token is required",
      });
    }

    // Get user profile from Auth0 using the access token
    const auth0User = await auth0Service.getUserProfile(access_token);

    // Find or create user with email-first account linking
    const user = await findOrCreateUserWithAccountLinking(auth0User);

    // Check if user needs to create/join organization
    const needsOrganization = user.organizationMemberships.length === 0;

    logger.info("Social login successful", {
      userId: user.id,
      email: user.email,
      provider: "social",
      needsOrganization,
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          organizations: user.organizationMemberships.map(
            (membership: OrganizationMembership) => ({
              id: membership.organization.id,
              name: membership.organization.name,
              slug: membership.organization.slug,
              role: membership.role,
            })
          ),
        },
        tokens: {
          accessToken: access_token,
          tokenType: "Bearer",
        },
        needsOrganization,
      },
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Social login failed", { error: errorMessage });

    res.status(401).json({
      error: "Authentication Failed",
      message: errorMessage,
    });
  }
});

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
router.post("/refresh", authenticate, async (req: Request, res: Response) => {
  try {
    // Since we're using Auth0, we don't handle refresh tokens directly
    // Instead, we validate the current token and return a success response
    // The frontend should use Auth0's getTokenSilently() for actual refresh

    res.json({
      success: true,
      message: "Token is still valid",
      data: {
        // Return the same token info since Auth0 handles refresh internally
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      },
    });
  } catch {
    res.status(401).json({
      error: "Token Refresh Failed",
      message: "Please login again",
    });
  }
});

/**
 * POST /api/auth/social-login-sync
 * Sync social login user with local database
 */
router.post(
  "/social-login-sync",
  authenticate,
  async (req: Request, res: Response) => {
    try {
      let { auth0Id, email, name, picture } = req.body;
      const { provider } = req.body;

      logger.info("🔍 Social login sync started", {
        auth0Id,
        email,
        name,
        provider,
        requestBody: req.body,
        hasAuthUser: !!req.user,
      });

      // Always try to get fresh user info from Auth0 userinfo endpoint
      // This bypasses any JWT token claim issues
      logger.info("🔄 Fetching fresh user info from Auth0 userinfo endpoint");

      try {
        // Extract the token from Authorization header
        const authHeader = req.headers.authorization;
        const token = authHeader?.substring(7); // Remove "Bearer " prefix

        if (token) {
          const auth0User = await auth0Service.getUserProfile(token);

          // Use Auth0 userinfo as the source of truth, fallback to request body
          auth0Id = auth0User.user_id || auth0Id;
          email = auth0User.email || email;
          name = auth0User.name || name;
          picture = auth0User.picture || picture;

          logger.info("✅ Successfully fetched user info from Auth0 userinfo", {
            auth0Id,
            email,
            name,
            hasPicture: !!picture,
            source: "auth0-userinfo",
          });
        } else {
          logger.warn("⚠️ No token found, using request body data");
        }
      } catch (auth0Error: unknown) {
        const errorMessage =
          auth0Error instanceof Error ? auth0Error.message : "Unknown error";
        logger.error(
          "❌ Failed to fetch user info from Auth0, using request body",
          {
            error: errorMessage,
            fallbackEmail: email,
          }
        );
      }

      if (!auth0Id || !email) {
        logger.error("❌ Social login sync validation failed", {
          auth0Id,
          email,
          missingFields: {
            auth0Id: !auth0Id,
            email: !email,
          },
        });
        return res.status(400).json({
          error: "Validation Error",
          message: "auth0Id and email are required",
        });
      }

      // Create a mock auth0User object for the helper function
      const mockAuth0User: Auth0User = {
        user_id: auth0Id,
        email,
        name,
        picture,
      };

      logger.info("🔄 Calling findOrCreateUserWithAccountLinking", {
        mockAuth0User,
      });

      // Find or create user with email-first account linking
      const user = await findOrCreateUserWithAccountLinking(mockAuth0User);

      logger.info("✅ Social login sync completed", {
        userId: user.id,
        email: user.email,
        auth0Id: user.auth0Id,
        provider,
        organizationCount: user.organizationMemberships?.length || 0,
      });

      const needsOrganization = user.organizationMemberships.length === 0;

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            organizations: user.organizationMemberships.map(
              (membership: OrganizationMembership) => ({
                id: membership.organization.id,
                name: membership.organization.name,
                slug: membership.organization.slug,
                role: membership.role,
              })
            ),
          },
          needsOrganization,
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const errorStack = error instanceof Error ? error.stack : undefined;
      logger.error("❌ Social login sync failed", {
        error: errorMessage,
        stack: errorStack,
        auth0Id: req.body.auth0Id,
        email: req.body.email,
        provider: req.body.provider,
      });

      res.status(500).json({
        error: "Social Login Sync Failed",
        message: "Failed to sync user data",
      });
    }
  }
);

/**
 * POST /api/auth/social-create-user
 * Alternative endpoint for social login that bypasses JWT token issues
 * Uses Auth0 userinfo endpoint directly
 */
router.post("/social-create-user", async (req: Request, res: Response) => {
  try {
    const { access_token } = req.body;

    logger.info("🔄 Social create user started", {
      hasToken: !!access_token,
    });

    if (!access_token) {
      return res.status(400).json({
        error: "Validation Error",
        message: "Access token is required",
      });
    }

    // Get user profile directly from Auth0 userinfo
    const auth0User = await auth0Service.getUserProfile(access_token);

    logger.info("✅ Got user info from Auth0 userinfo", {
      user_id: auth0User.user_id,
      email: auth0User.email,
      name: auth0User.name,
    });

    // Find or create user with email-first account linking
    const user = await findOrCreateUserWithAccountLinking(auth0User);

    logger.info("✅ Social create user completed", {
      userId: user.id,
      email: user.email,
      auth0Id: user.auth0Id,
      organizationCount: user.organizationMemberships?.length || 0,
    });

    const needsOrganization = user.organizationMemberships.length === 0;

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          organizations: user.organizationMemberships.map(
            (membership: OrganizationMembership) => ({
              id: membership.organization.id,
              name: membership.organization.name,
              slug: membership.organization.slug,
              role: membership.role,
            })
          ),
        },
        needsOrganization,
      },
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    const errorStack = error instanceof Error ? error.stack : undefined;
    logger.error("❌ Social create user failed", {
      error: errorMessage,
      stack: errorStack,
    });

    res.status(500).json({
      error: "Social User Creation Failed",
      message: "Failed to create user from social login",
    });
  }
});

export default router;
