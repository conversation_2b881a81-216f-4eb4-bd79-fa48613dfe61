import express from "express";
import { Request, Response } from "express";
import { authenticate } from "../middleware/auth.middleware";
import { chatService } from "../services/chat.service";
import { logger } from "../utils/logger";
import { prisma } from "@/config/database";
import { sseService } from "../services/sse.service";
import { ConversationVisibility } from "@prisma/client";

const router: express.Router = express.Router();

// Apply auth middleware to all routes
router.use(authenticate);

/**
 * POST /api/chat/conversations
 * Create a new conversation
 */
router.post("/conversations", async (req: Request, res: Response) => {
  try {
    const { title, organizationId, visibility } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required in request body",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    // Validate visibility if provided
    const conversationVisibility =
      visibility && Object.values(ConversationVisibility).includes(visibility)
        ? visibility
        : ConversationVisibility.private;

    const conversation = await chatService.createConversation(
      userId,
      organizationId,
      title,
      conversationVisibility
    );

    // Transform conversation to frontend format
    const formattedConversation = {
      id: conversation.id,
      title: conversation.title,
      createdAt: conversation.createdAt.toISOString(),
      updatedAt: conversation.updatedAt.toISOString(),
      visibility: conversation.visibility,
      userId: conversation.userId,
    };

    res.status(201).json({
      success: true,
      data: formattedConversation,
    });
  } catch (error) {
    logger.error("Error creating conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create conversation",
    });
  }
});

/**
 * GET /api/chat/conversations
 * List user's conversations for an organization
 */
router.get("/conversations", async (req: Request, res: Response) => {
  try {
    const { organizationId, filter } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required as a query parameter",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    // Validate filter parameter
    const validFilters = ["my-chats", "public-chats", "all"];
    const conversationFilter =
      filter && validFilters.includes(filter as string)
        ? (filter as "my-chats" | "public-chats" | "all")
        : undefined;

    const conversations = await chatService.listConversations(
      userId,
      organizationId as string,
      conversationFilter
    );

    // Transform conversations to frontend format
    const formattedConversations = conversations.map((conv) => ({
      id: conv.id,
      title: conv.title,
      createdAt: conv.createdAt.toISOString(),
      updatedAt: conv.updatedAt.toISOString(),
      visibility: conv.visibility,
      userId: conv.userId,
      user: conv.user
        ? {
            id: conv.user.id,
            name: conv.user.name,
            email: conv.user.email,
            avatar: conv.user.avatar,
          }
        : undefined,
    }));

    res.json({
      success: true,
      data: { conversations: formattedConversations },
    });
  } catch (error) {
    logger.error("Error listing conversations:", error);
    res.status(500).json({
      success: false,
      message: "Failed to list conversations",
    });
  }
});

/**
 * GET /api/chat/conversations/:id
 * Get conversation with messages
 */
router.get("/conversations/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { organizationId } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required as a query parameter",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    const conversation = await chatService.getConversation(
      id,
      userId,
      organizationId as string
    );

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: "Conversation not found or access denied",
      });
    }

    // Transform messages to frontend format
    const messages =
      conversation.unifiedMessages?.map((msg) => ({
        id: msg.id,
        content: msg.content,
        role: msg.role.toLowerCase() as "user" | "assistant",
        createdAt: msg.createdAt.toISOString(),
        references: msg.references,
        annotations: msg.annotations,
        messageDocuments: msg.messageDocuments,
      })) || [];

    res.json({
      success: true,
      data: {
        conversation: {
          id: conversation.id,
          title: conversation.title,
          createdAt: conversation.createdAt.toISOString(),
          updatedAt: conversation.updatedAt.toISOString(),
          visibility: conversation.visibility,
          userId: conversation.userId,
          user: conversation.user
            ? {
                id: conversation.user.id,
                name: conversation.user.name,
                email: conversation.user.email,
                avatar: conversation.user.avatar,
              }
            : undefined,
        },
        messages,
      },
    });
  } catch (error) {
    logger.error("Error getting conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get conversation",
    });
  }
});

/**
 * POST /api/chat/conversations/:id/messages/stream
 * Send message with streaming response using SSE
 */
router.post(
  "/conversations/:id/messages/stream",
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { message, settings, files } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!message) {
        return res.status(400).json({
          success: false,
          message: "Message is required",
        });
      }

      // Get organization ID from request body
      const { organizationId } = req.body;

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required in request body",
        });
      }

      // Verify user has access to this organization
      const userOrg = req.user?.organizations.find(
        (org) => org.id === organizationId
      );
      if (!userOrg) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this organization",
        });
      }

      // Create SSE connection
      sseService.createConnection(id, userId, res);

      // Stream the chat response
      await chatService.sendMessage(
        id,
        message,
        userId,
        organizationId,
        settings || {},
        files || []
      );

      // Send end event and close connection
      sseService.sendEnd(id, userId);
    } catch (error) {
      logger.error("Error in streaming endpoint:", error);

      // If headers not sent yet, send error response
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Failed to process message",
        });
      } else {
        // Send error through SSE service
        sseService.sendError(
          req.params.id,
          req.user?.id || "",
          "Internal server error",
          { error }
        );
      }
    } finally {
      // Close SSE connection
      sseService.closeConnection(`${req.params.id}-${req.user?.id}`);
    }
  }
);

/**
 * DELETE /api/chat/conversations/:id
 * Archive conversation
 */
router.delete("/conversations/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    await chatService.archiveConversation(id, userId);

    res.json({
      success: true,
      message: "Conversation archived successfully",
    });
  } catch (error) {
    logger.error("Error archiving conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to archive conversation",
    });
  }
});

/**
 * GET /api/chat/settings
 * Get user's chat settings
 */
router.get("/settings", async (req: Request, res: Response) => {
  try {
    const { organizationId } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required as a query parameter",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    const settings = await chatService.getChatSettings();

    res.json({
      success: true,
      data: { settings },
    });
  } catch (error) {
    logger.error("Error getting chat settings:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get chat settings",
    });
  }
});

/**
 * PUT /api/chat/settings
 * Update user's chat settings
 */
router.put("/settings", async (req: Request, res: Response) => {
  try {
    const { organizationId, settings } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId || !settings) {
      return res.status(400).json({
        success: false,
        message: "Organization ID and settings are required in request body",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    const updatedSettings = await chatService.updateChatSettings(
      userId,
      organizationId,
      settings
    );

    res.json({
      success: true,
      data: { settings: updatedSettings },
    });
  } catch (error) {
    logger.error("Error updating chat settings:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update chat settings",
    });
  }
});

/**
 * PUT /api/chat/conversations/:id/visibility
 * Update conversation visibility (only owner can update)
 */
router.put(
  "/conversations/:id/visibility",
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { visibility } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (
        !visibility ||
        !Object.values(ConversationVisibility).includes(visibility)
      ) {
        return res.status(400).json({
          success: false,
          message: "Valid visibility value is required (private or public)",
        });
      }

      const updatedConversation =
        await chatService.updateConversationVisibility(id, userId, visibility);

      if (!updatedConversation) {
        return res.status(404).json({
          success: false,
          message: "Conversation not found or access denied",
        });
      }

      res.json({
        success: true,
        data: {
          conversation: {
            id: updatedConversation.id,
            title: updatedConversation.title,
            createdAt: updatedConversation.createdAt.toISOString(),
            updatedAt: updatedConversation.updatedAt.toISOString(),
            visibility: updatedConversation.visibility,
            userId: updatedConversation.userId,
          },
        },
      });
    } catch (error) {
      logger.error("Error updating conversation visibility:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update conversation visibility",
      });
    }
  }
);

/**
 * PUT /api/chat/conversations/:id
 * Update conversation title
 */
router.put("/conversations/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { title } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!title || typeof title !== "string") {
      return res.status(400).json({
        success: false,
        message: "Title is required",
      });
    }

    // Update conversation title
    const updated = await prisma.conversation.update({
      where: { id, userId },
      data: { title },
    });

    if (!updated) {
      return res.status(404).json({
        success: false,
        message: "Conversation not found",
      });
    }

    res.json({
      success: true,
      data: {
        conversation: {
          id: updated.id,
          title: updated.title,
          createdAt: updated.createdAt.toISOString(),
          updatedAt: updated.updatedAt.toISOString(),
        },
      },
    });
  } catch (error) {
    logger.error("Error updating conversation title:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update conversation title",
    });
  }
});

export default router;
