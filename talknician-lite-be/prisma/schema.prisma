generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model User {
  id                      String               @id @default(cuid())
  email                   String               @unique
  name                    String?
  auth0Id                 String               @unique @map("auth0_id")
  avatar                  String?
  createdAt               DateTime             @default(now()) @map("created_at")
  updatedAt               DateTime             @updatedAt @map("updated_at")
  conversations           Conversation[]
  documents               Document[]
  organizationMemberships OrganizationMember[]
  personas                Persona[]
  scrapedWebsites         ScrapedWebsite[]
  unifiedMessages         UnifiedMessage[]
  userSessions            UserSession[]
  messageDocuments        MessageDocument[]
  // --- Guide system ---
  guides                  Guide[]
  guideComments           GuideComment[]
}

model Organization {
  id              String                   @id @default(cuid())
  name            String
  slug            String                   @unique
  description     String?
  avatar          String?
  vectorStoreId   String?                  @unique @map("vector_store_id")
  createdAt       DateTime                 @default(now()) @map("created_at")
  plan            PlanType                 @default(FREE)
  updatedAt       DateTime                 @updatedAt @map("updated_at")
  conversations   Conversation[]
  documents       Document[]
  invitations     OrganizationInvitation[]
  members         OrganizationMember[]
  personas        Persona[]
  scrapedWebsites ScrapedWebsite[]
  guides          Guide[]
  @@map("organizations")
}


model OrganizationMember {
  id                 String           @id @default(cuid())
  role               OrganizationRole
  status             MembershipStatus @default(ACTIVE)
  joinedAt           DateTime         @default(now()) @map("joined_at")
  organizationId     String           @map("organization_id")
  userId             String           @map("user_id")
  applicationMessage String?          @map("application_message")
  reviewedBy         String?          @map("reviewed_by")
  reviewedAt         DateTime?        @map("reviewed_at")
  reviewMessage      String?          @map("review_message")
  organization       Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user               User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@map("organization_members")
}

model OrganizationInvitation {
  id             String           @id @default(cuid())
  email          String
  role           OrganizationRole
  invitedBy      String           @map("invited_by")
  organizationId String           @map("organization_id")
  createdAt      DateTime         @default(now()) @map("created_at")
  expiresAt      DateTime         @map("expires_at")
  usedAt         DateTime?        @map("used_at")
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([email, organizationId])
  @@map("organization_invitations")
}

model Conversation {
  id              String                 @id @default(cuid())
  title           String
  createdAt       DateTime               @default(now()) @map("created_at")
  isArchived      Boolean                @default(false) @map("is_archived")
  organizationId  String                 @map("organization_id")
  updatedAt       DateTime               @updatedAt @map("updated_at")
  userId          String                 @map("user_id")
  visibility      ConversationVisibility @default(private)
  organization    Organization           @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user            User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  unifiedMessages UnifiedMessage[]

  @@index([organizationId, visibility])
  @@map("conversations")
}

model Document {
  id                 String              @id @default(cuid())
  filename           String
  size               Int
  azureBlobUrl       String?             @map("azure_blob_url")
  localPath          String?             @map("local_path")
  openaiFileId       String?             @unique @map("openai_file_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  mimeType           String              @map("mime_type")
  organizationId     String              @map("organization_id")
  originalName       String              @map("original_name")
  status             DocumentStatus      @default(PROCESSING)
  updatedAt          DateTime            @updatedAt @map("updated_at")
  userId             String              @map("user_id")
  uploadedBy         String              @map("uploaded_by")
  isDeleted          Boolean             @default(false) @map("is_deleted")
  organization       Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  oneDriveFileId     String?             @unique @map("one_drive_file_id")
  
  messageAnnotations MessageAnnotation[]
  messageReferences  MessageReference[]
  scrapedWebsite     ScrapedWebsite?

  @@map("documents")
}


model UserSession {
  id           String    @id @default(cuid())
  userId       String    @map("user_id")
  refreshToken String    @unique @map("refresh_token")
  expiresAt    DateTime  @map("expires_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  revokedAt    DateTime? @map("revoked_at")
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model Persona {
  id             String       @id @default(cuid())
  title          String
  systemPrompt   String       @map("system_prompt")
  userId         String?      @map("user_id")
  organizationId String       @map("organization_id")
  isPublic       Boolean      @default(false) @map("is_public")
  isDefault      Boolean      @default(false) @map("is_default")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("personas")
}

model ScrapedWebsite {
  id                 String              @id @default(cuid())
  url                String
  title              String
  content            String
  markdown           String?
  metadata           Json?
  documentId         String?             @unique @map("document_id")
  organizationId     String              @map("organization_id")
  userId             String              @map("user_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  status             DocumentStatus      @default(PROCESSING)
  messageAnnotations MessageAnnotation[]
  messageReferences  MessageReference[]
  document           Document?           @relation(fields: [documentId], references: [id])
  organization       Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("scraped_websites")
}

model UnifiedMessage {
  id              String              @id @default(cuid())
  role            MessageRole
  content         String
  conversationId  String              @map("conversation_id")
  userId          String              @map("user_id")
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")
  openaiMessageId String?             @map("openai_message_id")
  openaiItemId    String?             @map("openai_item_id")
  sequenceNumber  Int?                @map("sequence_number")
  metadata        Json?
  annotations     MessageAnnotation[]
  references      MessageReference[]
  conversation    Conversation        @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  messageDocuments MessageDocument[]

  @@map("unified_messages")
}

model MessageAnnotation {
  id               String          @id @default(cuid())
  type             AnnotationType
  startIndex       Int             @map("start_index")
  endIndex         Int             @map("end_index")
  url              String?
  title            String?
  fileId           String?         @map("file_id")
  filename         String?
  documentId       String?         @map("document_id")
  websiteId        String?         @map("website_id")
  unifiedMessageId String          @map("unified_message_id")
  document         Document?       @relation(fields: [documentId], references: [id])
  unifiedMessage   UnifiedMessage  @relation(fields: [unifiedMessageId], references: [id], onDelete: Cascade)
  website          ScrapedWebsite? @relation(fields: [websiteId], references: [id])

  @@map("message_annotations")
}

model MessageReference {
  id               String          @id @default(cuid())
  type             ReferenceType
  title            String
  documentId       String?         @map("document_id")
  azureBlobUrl     String?         @map("azure_blob_url")
  openaiFileId     String?         @map("openai_file_id")
  filename         String?
  websiteId        String?         @map("website_id")
  url              String?
  scrapedContent   String?         @map("scraped_content")
  metadata         Json?
  createdAt        DateTime        @default(now()) @map("created_at")
  unifiedMessageId String          @map("unified_message_id")
  document         Document?       @relation(fields: [documentId], references: [id])
  unifiedMessage   UnifiedMessage  @relation(fields: [unifiedMessageId], references: [id], onDelete: Cascade)
  website          ScrapedWebsite? @relation(fields: [websiteId], references: [id])

  @@map("message_references")
}

model MessageDocument {
  id               String         @id @default(cuid())
  filename         String
  size             Int
  mimeType         String
  azureBlobUrl     String?        @map("azure_blob_url")
  uploadedAt       DateTime       @default(now())
  messageId        String?        @map("message_id")
  userId           String?        @map("user_id")
  documentId       String?        @map("document_id")
  unifiedMessage   UnifiedMessage? @relation(fields: [messageId], references: [id], onDelete:Cascade)
  user             User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
  openaiFileId     String?         @map("openai_file_id")

  @@map("message_documents")
}

// --- Guide System Models ---
model Guide {
  id              String        @id @default(cuid())
  title           String
  description     String?
  thumbnail       String?
  status          GuideStatus   @default(DRAFT)
  visibility      GuideVisibility @default(PRIVATE)
  transcription   String?
  summary         String?
  duration        Int?
  videoUrl        String?
  azureBlobUrl    String?
  externalJobId   String?       @map("external_job_id") // ID from external service
  externalJobStatus String?     @map("external_job_status") // Status from external service
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  publishedAt     DateTime?     @map("published_at")
  userId          String        @map("user_id")
  organizationId  String        @map("organization_id")
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization    Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  steps           GuideStep[]
  comments        GuideComment[]
  @@map("guides")
}

model GuideStep {
  id              String        @id @default(cuid())
  title           String
  description     String?
  stepNumber      Int           @map("step_number")
  mediaId         String?       @map("media_id")
  media           Media?        @relation(fields: [mediaId], references: [id], onDelete: SetNull)
  startTime       Int?          @map("start_time")
  endTime         Int?          @map("end_time")
  stepSummary     String?       @map("step_summary")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  guideId         String        @map("guide_id")
  guide           Guide         @relation(fields: [guideId], references: [id], onDelete: Cascade)
  @@map("guide_steps")
}

model Media {
  id              String        @id @default(cuid())
  type            MediaType
  url             String
  azureBlobUrl    String?
  thumbnail       String?
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  // Relations
  steps           GuideStep[]
  @@map("media")
}

model GuideComment {
  id              String        @id @default(cuid())
  content         String
  isEdited        Boolean       @default(false) @map("is_edited")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  guideId         String        @map("guide_id")
  userId          String        @map("user_id")
  parentId        String?       @map("parent_id")
  guide           Guide         @relation(fields: [guideId], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent          GuideComment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies         GuideComment[] @relation("CommentReplies")
  @@map("guide_comments")
}

// --- Standard Enums (required by existing models) ---
enum PlanType {
  FREE
  PRO
  ENTERPRISE
}
enum OrganizationRole {
  OWNER
  MANAGER
  MEMBER
}
enum MembershipStatus {
  PENDING
  ACTIVE
  REJECTED
}
enum MessageRole {
  user
  assistant
  developer
}
enum DocumentStatus {
  PROCESSING
  COMPLETED
  FAILED
}
enum AnnotationType {
  URL_CITATION
  FILE_CITATION
}
enum ReferenceType {
  DOCUMENT
  WEBSITE
}
enum ConversationVisibility {
  private
  public
}
// --- Guide System Enums ---
enum GuideStatus {
  DRAFT
  PROCESSING
  REVIEW
  PUBLISHED
  ARCHIVED
}
enum GuideVisibility {
  PRIVATE
  ORGANIZATION
  PUBLIC
}
enum MediaType {
  VIDEO
  IMAGE
}


