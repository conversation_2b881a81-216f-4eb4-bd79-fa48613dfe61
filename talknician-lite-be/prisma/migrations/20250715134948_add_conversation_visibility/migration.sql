-- Create<PERSON><PERSON>
CREATE TYPE "ConversationVisibility" AS ENUM ('private', 'public');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "GuideStatus" AS ENUM ('DRAFT', 'PROCESSING', 'REVIEW', 'PUBLISHED', 'ARCHIVED');

-- Create<PERSON><PERSON>
CREATE TYPE "GuideVisibility" AS ENUM ('PRIVATE', 'ORGANIZATION', 'PUBLIC');

-- C<PERSON><PERSON>num
CREATE TYPE "MediaType" AS ENUM ('VIDEO', 'IMAGE');

-- AlterTable
ALTER TABLE "conversations" ADD COLUMN     "visibility" "ConversationVisibility" NOT NULL DEFAULT 'private';

-- CreateTable
CREATE TABLE "guides" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "thumbnail" TEXT,
    "status" "GuideStatus" NOT NULL DEFAULT 'DRAFT',
    "visibility" "GuideVisibility" NOT NULL DEFAULT 'PRIVATE',
    "transcription" TEXT,
    "summary" TEXT,
    "duration" INTEGER,
    "videoUrl" TEXT,
    "azureBlobUrl" TEXT,
    "external_job_id" TEXT,
    "external_job_status" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "published_at" TIMESTAMP(3),
    "user_id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,

    CONSTRAINT "guides_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guide_steps" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "step_number" INTEGER NOT NULL,
    "media_id" TEXT,
    "start_time" INTEGER,
    "end_time" INTEGER,
    "step_summary" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "guide_id" TEXT NOT NULL,

    CONSTRAINT "guide_steps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "media" (
    "id" TEXT NOT NULL,
    "type" "MediaType" NOT NULL,
    "url" TEXT NOT NULL,
    "azureBlobUrl" TEXT,
    "thumbnail" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "media_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "guide_comments" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "is_edited" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "guide_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "parent_id" TEXT,

    CONSTRAINT "guide_comments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "conversations_organization_id_visibility_idx" ON "conversations"("organization_id", "visibility");

-- AddForeignKey
ALTER TABLE "guides" ADD CONSTRAINT "guides_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guides" ADD CONSTRAINT "guides_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guide_steps" ADD CONSTRAINT "guide_steps_media_id_fkey" FOREIGN KEY ("media_id") REFERENCES "media"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guide_steps" ADD CONSTRAINT "guide_steps_guide_id_fkey" FOREIGN KEY ("guide_id") REFERENCES "guides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guide_comments" ADD CONSTRAINT "guide_comments_guide_id_fkey" FOREIGN KEY ("guide_id") REFERENCES "guides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guide_comments" ADD CONSTRAINT "guide_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guide_comments" ADD CONSTRAINT "guide_comments_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "guide_comments"("id") ON DELETE SET NULL ON UPDATE CASCADE;
