# Talknician Lite Backend API

A modern Express.js API with TypeScript, Prisma, PostgreSQL, and Auth0 authentication for the Talknician Lite application.

## 🚀 Features

- **Custom Auth0 Integration**: Email/password login with custom UI (no Auth0 hosted login)
- **Organization Management**: Multi-tenant workspace system
- **PostgreSQL + Prisma**: Type-safe database operations
- **TypeScript**: Full type safety throughout the application
- **Azure Storage**: File upload and document management
- **OpenAI Integration**: Ready for AI chat functionality
- **Production Ready**: Security middleware, rate limiting, logging

## 🛠️ Tech Stack

- **Framework**: Express.js + TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Auth0 (Resource Owner Password Grant)
- **Storage**: Azure Blob Storage
- **AI**: OpenAI GPT integration
- **Logging**: Winston
- **Validation**: express-validator + Zod

## 📋 Prerequisites

- Node.js 18+ and pnpm
- PostgreSQL database (Azure PostgreSQL recommended)
- Auth0 account with application configured
- Azure Storage account

## ⚡ Quick Start

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Environment Setup

Create a `.env` file from the template:

```bash
cp env.template .env
```

### 3. Database Setup

```bash
# Generate Prisma client
pnpm db:generate

# Create database tables
pnpm db:push

# Optional: Open Prisma Studio to view data
pnpm db:studio
```

### 4. Start Development Server

```bash
pnpm dev

# Server will start on http://localhost:8000
```

## 🔐 Auth0 Configuration

### Step 1: Auth0 Application Settings

In your Auth0 dashboard:

1. Go to **Applications** → Your Application
2. **Settings** tab:
   - **Application Type**: Regular Web Application
   - **Allowed Callback URLs**: `http://localhost:3000/api/auth/callback`
   - **Allowed Logout URLs**: `http://localhost:3000`
   - **Allowed Web Origins**: `http://localhost:3000`

### Step 2: Enable Resource Owner Password Grant

1. **Applications** → Your Application → **Settings**
2. **Advanced Settings** → **Grant Types**
3. Enable **Password** grant type
4. Save changes

⚠️ **Important**: This grant type should only be used for first-party applications where you control both the frontend and backend.

### Step 3: Create Auth0 API

1. Go to **APIs** in Auth0 dashboard
2. Click **Create API**
3. **Name**: `Talknician API`
4. **Identifier**: `https://talknician-api` (must match AUTH0_AUDIENCE)
5. **Signing Algorithm**: `RS256`

### Step 4: Management API (Optional - for user creation)

1. Go to **APIs** → **Auth0 Management API**
2. **Machine to Machine Applications** tab
3. Authorize your application with these scopes:
   - `create:users`
   - `read:users`
   - `update:users`
   - `create:users_app_metadata`

## 🔧 How to Verify Auth0 Setup

### Test 1: Health Check

```bash
curl http://localhost:8000/health
```

### Test 2: User Signup

```bash
curl -X POST http://localhost:8000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "name": "Test User"
  }'
```

### Test 3: User Login

```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!"
  }'
```

### Test 4: Protected Route

```bash
# Use the access_token from login response
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📚 API Endpoints

### Authentication

| Method | Endpoint                    | Description               |
| ------ | --------------------------- | ------------------------- |
| POST   | `/api/auth/signup`          | Create new user account   |
| POST   | `/api/auth/login`           | Login with email/password |
| GET    | `/api/auth/me`              | Get current user profile  |
| POST   | `/api/auth/forgot-password` | Request password reset    |

### Organization Management

| Method | Endpoint                        | Description                |
| ------ | ------------------------------- | -------------------------- |
| POST   | `/api/auth/create-organization` | Create new organization    |
| POST   | `/api/auth/join-organization`   | Join existing organization |

### System

| Method | Endpoint  | Description           |
| ------ | --------- | --------------------- |
| GET    | `/health` | Health check endpoint |

## 🔄 Auth0 Login Flow

```mermaid
sequenceDiagram
    participant Frontend
    participant API
    participant Auth0
    participant Database

    Frontend->>API: POST /api/auth/login {email, password}
    API->>Auth0: Resource Owner Password Grant
    Auth0->>API: Access Token + User Info
    API->>Database: Find/Create User
    API->>Frontend: User Data + Tokens + needsOrganization flag

    alt User needs organization
        Frontend->>API: POST /api/auth/create-organization
        API->>Database: Create Organization + Membership
        API->>Frontend: Organization created
    else User joins existing org
        Frontend->>API: POST /api/auth/join-organization
        API->>Database: Add Membership
        API->>Frontend: Joined organization
    end
```

## 🏗️ Project Structure

```
src/
├── config/              # Configuration files
│   ├── index.ts         # Main config with env validation
│   └── database.ts      # Prisma client setup
├── middleware/          # Express middleware
│   └── auth.middleware.ts
├── routes/              # API route handlers
│   └── auth.routes.ts
├── services/            # Business logic services
│   └── auth0.service.ts
├── utils/               # Utility functions
│   └── logger.ts
└── index.ts             # Main application entry point

prisma/
└── schema.prisma        # Database schema

```

## 🧪 Testing the Setup

1. **Start the server**: `pnpm dev`
2. **Check health**: Visit `http://localhost:8000/health`
3. **Test signup**: Use curl or Postman to create a user
4. **Test login**: Login with the created user
5. **Create organization**: Test organization creation flow

## 🔧 Development Commands

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server

# Database
pnpm db:generate      # Generate Prisma client
pnpm db:push          # Push schema to database
pnpm db:migrate       # Create and run migrations
pnpm db:studio        # Open Prisma Studio

# Utilities
pnpm type-check       # Run TypeScript type checking
```

## 🚀 Deployment

### Environment Variables for Production

Make sure to set these in your production environment:

```env
NODE_ENV=production
DATABASE_URL=your-production-db-url
AUTH0_CLIENT_SECRET=your-production-secret
JWT_SECRET=your-production-jwt-secret
# ... other production values
```

### Build and Run

```bash
pnpm build
pnpm start
```

## 🤝 Integration with Frontend

The frontend should:

1. **Use custom login form** (not Auth0 Universal Login)
2. **Call `/api/auth/login`** with email/password
3. **Handle `needsOrganization` flag** to show org creation/join flow
4. **Store access token** and include in Authorization header
5. **Call `/api/auth/me`** to get user context

## 🔍 Troubleshooting

### Auth0 "Unauthorized" Error

- Verify Resource Owner Password Grant is enabled
- Check CLIENT_ID and CLIENT_SECRET are correct
- Ensure user exists in Auth0

### Database Connection Error

- Verify DATABASE_URL is correct
- Check PostgreSQL server is running
- Ensure database exists

### CORS Issues

- Verify FRONTEND_URL in config matches your frontend URL
- Check CORS configuration in `src/index.ts`

## 📝 License

MIT License - see LICENSE file for details.
