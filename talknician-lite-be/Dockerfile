# ---- Base Node image ----
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install OpenSSL for Prisma
RUN apk add --no-cache openssl

# Install pnpm
RUN npm install -g pnpm

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

COPY . .

# Copy Prisma schema and migrations
COPY prisma ./prisma

# Generate Prisma client
RUN npx prisma generate

# Build TypeScript
RUN pnpm build

# ---- Release image ----
FROM node:18-alpine AS release
WORKDIR /app

# Install OpenSSL for Prisma
RUN apk add --no-cache openssl

# Install pnpm
RUN npm install -g pnpm

# Copy only production dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --prod --frozen-lockfile

# Copy built app and necessary files
COPY --from=base /app/dist ./dist
COPY --from=base /app/prisma ./prisma

# Generate Prisma client in the release image
RUN npx prisma generate

# Expose port (Azure uses PORT env var)
EXPOSE 3002

# Start the app
CMD ["node", "dist/index.js"] 